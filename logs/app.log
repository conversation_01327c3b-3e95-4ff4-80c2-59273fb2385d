2025-06-26 17:33:33.323  INFO 21260 --- [main] cientificEvaluationUserServerApplication : Starting ScientificEvaluationUserServerApplication using Java 17.0.12 on DESKTOP-PFSO889 with PID 21260 (C:\Users\<USER>\Desktop\scientific-literacy-evaluation-system\target\classes started by lqj in C:\Users\<USER>\Desktop\scientific-literacy-evaluation-system)
2025-06-26 17:33:33.325 DEBUG 21260 --- [main] cientificEvaluationUserServerApplication : Running with Spring Boot v2.7.3, Spring v5.3.22
2025-06-26 17:33:33.326  INFO 21260 --- [main] cientificEvaluationUserServerApplication : The following 1 profile is active: "dev"
2025-06-26 17:33:34.010  INFO 21260 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-26 17:33:34.065  INFO 21260 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 52 ms. Found 4 MongoDB repository interfaces.
2025-06-26 17:33:34.582  INFO 21260 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-06-26 17:33:34.599  INFO 21260 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-26 17:33:34.600  INFO 21260 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-26 17:33:34.684  INFO 21260 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-26 17:33:34.685  INFO 21260 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1327 ms
2025-06-26 17:33:34.951  INFO 21260 --- [main] org.mongodb.driver.client                : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/JetBrains s.r.o./17.0.12+1-b1207.37"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@22e813fc], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@9880a15]}, clusterSettings={hosts=[*************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@7e4ac598], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-06-26 17:33:34.961  INFO 21260 --- [cluster-rtt-ClusterId{value='685d13ee345ea756195b3a8e', description='null'}-*************:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:2, serverValue:226908}] to *************:27017
2025-06-26 17:33:34.961  INFO 21260 --- [cluster-ClusterId{value='685d13ee345ea756195b3a8e', description='null'}-*************:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:1, serverValue:226909}] to *************:27017
2025-06-26 17:33:34.962  INFO 21260 --- [cluster-ClusterId{value='685d13ee345ea756195b3a8e', description='null'}-*************:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=*************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=14355800}
2025-06-26 17:33:35.004  WARN 21260 --- [main] o.s.data.convert.CustomConversions       : Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-06-26 17:33:35.111  WARN 21260 --- [main] o.s.data.convert.CustomConversions       : Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-06-26 17:33:35.317  INFO 21260 --- [main] c.zkdiman.common.utils.TencentCosUtils   : 腾讯云COS客户端初始化成功，bucket: iai-1311740348, region: ap-nanjing
2025-06-26 17:33:35.322  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : 开始创建MongoDB索引...
2025-06-26 17:33:35.348  INFO 21260 --- [main] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:3, serverValue:226910}] to *************:27017
2025-06-26 17:33:35.370  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionSignUpLog集合的examCard字段索引已存在，跳过创建
2025-06-26 17:33:35.370  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionSignUpLog集合的name字段索引已存在，跳过创建
2025-06-26 17:33:35.370  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionSignUpLog集合的school字段索引已存在，跳过创建
2025-06-26 17:33:35.370  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionSignUpLog集合的examID字段索引已存在，跳过创建
2025-06-26 17:33:35.370  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionSignUpLog集合的grade字段索引已存在，跳过创建
2025-06-26 17:33:35.370  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionSignUpLog索引创建完成
2025-06-26 17:33:35.374  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionQuestionLog集合的examCard字段索引已存在，跳过创建
2025-06-26 17:33:35.375  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionQuestionLog集合的examId字段索引已存在，跳过创建
2025-06-26 17:33:35.375  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : CompetitionQuestionLog索引创建完成
2025-06-26 17:33:35.375  INFO 21260 --- [main] c.z.server.config.MongoIndexConfig       : MongoDB索引创建完成
2025-06-26 17:33:35.585 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.587 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.595  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: ExamInfoImportStrategy
方法名: getOrder
返回结果: 3
执行时间: 3ms
执行状态: 成功
记录时间: 2025-06-26T17:33:35.587209900
================================

2025-06-26 17:33:35.596 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.BasicInfoImportStrategy.getOrder
2025-06-26 17:33:35.596 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.BasicInfoImportStrategy.getOrder
2025-06-26 17:33:35.598  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: BasicInfoImportStrategy
方法名: getOrder
返回结果: 1
执行时间: 2ms
执行状态: 成功
记录时间: 2025-06-26T17:33:35.596810600
================================

2025-06-26 17:33:35.599 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.599 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.601  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: SchoolInfoImportStrategy
方法名: getOrder
返回结果: 2
执行时间: 2ms
执行状态: 成功
记录时间: 2025-06-26T17:33:35.599811
================================

2025-06-26 17:33:35.601 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.602 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.602  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: ExamInfoImportStrategy
方法名: getOrder
返回结果: 3
执行状态: 成功
记录时间: 2025-06-26T17:33:35.602811100
================================

2025-06-26 17:33:35.602 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.602 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.602  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: SchoolInfoImportStrategy
方法名: getOrder
返回结果: 2
执行状态: 成功
记录时间: 2025-06-26T17:33:35.602811100
================================

2025-06-26 17:33:35.602 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.602 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.602  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: ExamInfoImportStrategy
方法名: getOrder
返回结果: 3
执行状态: 成功
记录时间: 2025-06-26T17:33:35.602811100
================================

2025-06-26 17:33:35.602 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.603 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.603  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: SchoolInfoImportStrategy
方法名: getOrder
返回结果: 2
执行状态: 成功
记录时间: 2025-06-26T17:33:35.603821900
================================

2025-06-26 17:33:35.603 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.BasicInfoImportStrategy.getOrder
2025-06-26 17:33:35.603 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.BasicInfoImportStrategy.getOrder
2025-06-26 17:33:35.603  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: BasicInfoImportStrategy
方法名: getOrder
返回结果: 1
执行状态: 成功
记录时间: 2025-06-26T17:33:35.603821900
================================

2025-06-26 17:33:35.603 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.ScoreInfoImportStrategy.getOrder
2025-06-26 17:33:35.603 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.ScoreInfoImportStrategy.getOrder
2025-06-26 17:33:35.605  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: ScoreInfoImportStrategy
方法名: getOrder
返回结果: 4
执行时间: 2ms
执行状态: 成功
记录时间: 2025-06-26T17:33:35.603821900
================================

2025-06-26 17:33:35.606 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.606 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.SchoolInfoImportStrategy.getOrder
2025-06-26 17:33:35.606  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: SchoolInfoImportStrategy
方法名: getOrder
返回结果: 2
执行状态: 成功
记录时间: 2025-06-26T17:33:35.606894900
================================

2025-06-26 17:33:35.606 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.ScoreInfoImportStrategy.getOrder
2025-06-26 17:33:35.606 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.ScoreInfoImportStrategy.getOrder
2025-06-26 17:33:35.606  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: ScoreInfoImportStrategy
方法名: getOrder
返回结果: 4
执行状态: 成功
记录时间: 2025-06-26T17:33:35.606894900
================================

2025-06-26 17:33:35.606 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.606 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.studentimport.ExamInfoImportStrategy.getOrder
2025-06-26 17:33:35.607  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.studentimport
类名: ExamInfoImportStrategy
方法名: getOrder
返回结果: 3
执行状态: 成功
记录时间: 2025-06-26T17:33:35.607814200
================================

2025-06-26 17:33:35.629 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.export.ScreenshotExportStrategy.getSupportedTaskType
2025-06-26 17:33:35.629 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.export.ScreenshotExportStrategy.getSupportedTaskType
2025-06-26 17:33:35.631  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.export
类名: ScreenshotExportStrategy
方法名: getSupportedTaskType
返回结果: "EXAM_SCREENSHOTS"
执行时间: 2ms
执行状态: 成功
记录时间: 2025-06-26T17:33:35.629985700
================================

2025-06-26 17:33:35.632  INFO 21260 --- [main] c.z.s.s.Impl.AsyncExportTaskServiceImpl  : 异步导出服务策略初始化完成，支持的任务类型: [EXAM_SCREENSHOTS]
2025-06-26 17:33:35.686 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.strategy.export.ScreenshotExportStrategy.getSupportedTaskType
2025-06-26 17:33:35.686 DEBUG 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.strategy.export.ScreenshotExportStrategy.getSupportedTaskType
2025-06-26 17:33:35.686  INFO 21260 --- [main] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service.strategy.export
类名: ScreenshotExportStrategy
方法名: getSupportedTaskType
返回结果: "EXAM_SCREENSHOTS"
执行状态: 成功
记录时间: 2025-06-26T17:33:35.686484100
================================

2025-06-26 17:33:35.686  INFO 21260 --- [main] c.z.s.s.Impl.ExamExportServiceImpl       : 导出策略初始化完成，支持的任务类型: [EXAM_SCREENSHOTS]
2025-06-26 17:33:35.719  INFO 21260 --- [main] c.z.s.service.Impl.AdminServiceImpl      : AdminServiceImpl 初始化完成，注入了 4 个更新策略
2025-06-26 17:33:35.719 DEBUG 21260 --- [main] c.z.s.service.Impl.AdminServiceImpl      : 注入的策略: EnabledUpdateStrategy$$EnhancerBySpringCGLIB$$fa42f399
2025-06-26 17:33:35.719 DEBUG 21260 --- [main] c.z.s.service.Impl.AdminServiceImpl      : 注入的策略: NameUpdateStrategy$$EnhancerBySpringCGLIB$$172dee5
2025-06-26 17:33:35.719 DEBUG 21260 --- [main] c.z.s.service.Impl.AdminServiceImpl      : 注入的策略: PasswordUpdateStrategy$$EnhancerBySpringCGLIB$$b774a995
2025-06-26 17:33:35.719 DEBUG 21260 --- [main] c.z.s.service.Impl.AdminServiceImpl      : 注入的策略: UsernameUpdateStrategy$$EnhancerBySpringCGLIB$$b0143f66
2025-06-26 17:33:36.002  INFO 21260 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-26 17:33:36.131  INFO 21260 --- [main] c.z.server.config.WebMvcConfiguration    : 开发(非pro)环境已激活，正在创建 Knife4j/Swagger 接口文档...
2025-06-26 17:33:36.201  INFO 21260 --- [main] c.z.server.config.WebMvcConfiguration    : 扩展消息转换器...
2025-06-26 17:33:36.455  INFO 21260 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 17:33:36.754 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.CacheMonitorService.hourlyMonitor
2025-06-26 17:33:36.755 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.CacheMonitorService.hourlyMonitor
2025-06-26 17:33:36.756  INFO 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 开始每小时缓存监控...
2025-06-26 17:33:36.756  INFO 21260 --- [main] cientificEvaluationUserServerApplication : Started ScientificEvaluationUserServerApplication in 3.733 seconds (JVM running for 4.181)
2025-06-26 17:33:36.757  INFO 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存统计: 缓存统计信息:
- studentLoginCache: 0 条目
- studentExistsCache: 0 条目
- examCache: 0 条目
- taskCache: 0 条目
- schoolCache: 0 条目
- adminCache: 0 条目
总条目数: 0
内存使用: 2.04% (82 MB / 4032 MB)

2025-06-26 17:33:36.758  INFO 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service
类名: CacheMonitorService
方法名: hourlyMonitor
执行时间: 2ms
执行状态: 成功
记录时间: 2025-06-26T17:33:36.755106900
================================

2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.CacheMonitorService.monitorCacheSize
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.CacheMonitorService.monitorCacheSize
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 开始缓存大小监控...
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 当前内存使用率: {:.2f}%
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 studentLoginCache 当前大小: 0
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 studentExistsCache 当前大小: 0
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 examCache 当前大小: 0
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 taskCache 当前大小: 0
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 schoolCache 当前大小: 0
2025-06-26 17:33:36.758 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 adminCache 当前大小: 0
2025-06-26 17:33:36.758  INFO 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service
类名: CacheMonitorService
方法名: monitorCacheSize
执行状态: 成功
记录时间: 2025-06-26T17:33:36.758122200
================================

2025-06-26 17:33:37.228  INFO 21260 --- [RMI TCP Connection(4)-**************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 17:33:37.228  INFO 21260 --- [RMI TCP Connection(4)-**************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-26 17:33:37.230  INFO 21260 --- [RMI TCP Connection(4)-**************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-26 17:38:36.762 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.CacheMonitorService.monitorCacheSize
2025-06-26 17:38:36.762 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.CacheMonitorService.monitorCacheSize
2025-06-26 17:38:36.762 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 开始缓存大小监控...
2025-06-26 17:38:36.762 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 当前内存使用率: {:.2f}%
2025-06-26 17:38:36.762 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 studentLoginCache 当前大小: 0
2025-06-26 17:38:36.762 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 studentExistsCache 当前大小: 0
2025-06-26 17:38:36.763 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 examCache 当前大小: 0
2025-06-26 17:38:36.763 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 taskCache 当前大小: 0
2025-06-26 17:38:36.763 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 schoolCache 当前大小: 0
2025-06-26 17:38:36.763 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 adminCache 当前大小: 0
2025-06-26 17:38:36.763  INFO 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service
类名: CacheMonitorService
方法名: monitorCacheSize
执行时间: 1ms
执行状态: 成功
记录时间: 2025-06-26T17:38:36.762365200
================================

2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 检查日志配置: com.zkdiman.server.service.CacheMonitorService.monitorCacheSize
2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 方法在包含列表中，将记录日志: com.zkdiman.server.service.CacheMonitorService.monitorCacheSize
2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 开始缓存大小监控...
2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 当前内存使用率: {:.2f}%
2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 studentLoginCache 当前大小: 0
2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 studentExistsCache 当前大小: 0
2025-06-26 17:43:36.769 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 examCache 当前大小: 0
2025-06-26 17:43:36.770 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 taskCache 当前大小: 0
2025-06-26 17:43:36.770 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 schoolCache 当前大小: 0
2025-06-26 17:43:36.770 DEBUG 21260 --- [scheduling-1] c.z.server.service.CacheMonitorService   : 缓存 adminCache 当前大小: 0
2025-06-26 17:43:36.770  INFO 21260 --- [scheduling-1] com.zkdiman.common.aspect.LoggingAspect  : 
========== AOP日志记录 ==========
包名: com.zkdiman.server.service
类名: CacheMonitorService
方法名: monitorCacheSize
执行时间: 1ms
执行状态: 成功
记录时间: 2025-06-26T17:43:36.769808200
================================

2025-06-26 17:48:27.745  INFO 21260 --- [SpringApplicationShutdownHook] c.qcloud.cos.http.DefaultCosHttpClient   : shutdown stackTrace:
Class: com.qcloud.cos.http.DefaultCosHttpClient, Method: shutdown, Line: 225
Class: com.qcloud.cos.COSClient, Method: shutdown, Line: 182
Class: com.zkdiman.common.utils.TencentCosUtils, Method: destroyCosClient, Line: 77
Class: jdk.internal.reflect.NativeMethodAccessorImpl, Method: invoke0, Line: -2
Class: jdk.internal.reflect.NativeMethodAccessorImpl, Method: invoke, Line: 77
Class: jdk.internal.reflect.DelegatingMethodAccessorImpl, Method: invoke, Line: 43
Class: java.lang.reflect.Method, Method: invoke, Line: 569
Class: org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement, Method: invoke, Line: 389
Class: org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata, Method: invokeDestroyMethods, Line: 347
Class: org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor, Method: postProcessBeforeDestruction, Line: 177
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: destroy, Line: 197
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroyBean, Line: 587
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingleton, Line: 559
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingleton, Line: 1163
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingletons, Line: 520
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingletons, Line: 1156
Class: org.springframework.context.support.AbstractApplicationContext, Method: destroyBeans, Line: 1106
Class: org.springframework.context.support.AbstractApplicationContext, Method: doClose, Line: 1075
Class: org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext, Method: doClose, Line: 174
Class: org.springframework.context.support.AbstractApplicationContext, Method: close, Line: 1021
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: closeAndWait, Line: 145
Class: java.lang.Iterable, Method: forEach, Line: 75
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: run, Line: 114
Class: java.lang.Thread, Method: run, Line: 840
2025-06-26 17:48:27.745  INFO 21260 --- [SpringApplicationShutdownHook] c.zkdiman.common.utils.TencentCosUtils   : 腾讯云COS客户端已关闭
