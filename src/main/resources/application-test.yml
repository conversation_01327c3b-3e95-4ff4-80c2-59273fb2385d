# ===============================================
# 测试环境 (Development Profile) 配置
# ===============================================

# 自定义变量，用于存放开发环境的配置
zkdiman:
  mongodb:
    host: *************
    port: 27017
    database: science_literacy_assessment
    # 开发环境的 MongoDB 没有用户名和密码

# Spring Boot 配置
spring:
  data:
    mongodb:
      # 为【无密码认证】的 MongoDB 构造连接 URI
      uri: mongodb://${zkdiman.mongodb.host}:${zkdiman.mongodb.port}/${zkdiman.mongodb.database}

# Knife4j 配置
knife4j:
  enable: true        # 在开发环境启用 API 文档
  basic:
    enable: false     # 在开发环境【不启用】密码保护，方便调试

# 日志级别
logging:
  level:
    com.zkdiman: DEBUG # 开发环境可以设置更详细的日志级别