// 02-insert-test-data.js
// 此脚本为MongoDB集合插入测试数据（如果数据尚不存在）。

// Helper function to generate ObjectId (if your application uses it)
// MongoDB shell does not have ObjectId generation directly in this script context for older versions.
// We will use strings for IDs for simplicity in these test data scripts.
// If your application generates ObjectIds, ensure to use them or adjust your app logic.

const now = new Date().getTime();

// --- Admin --- (使用 username 作为唯一性检查)
db.Admin.countDocuments({ "username": { $in: ["admin1", "admin2", "admin3", "admin4", "admin5"] } }).then(count => {
  if (count === 0) {
    const adminsToInsert = [];
    for (let i = 1; i <= 5; i++) {
      adminsToInsert.push({
        _id: `admin_id_${i}`,
        username: `admin${i}`,
        password: `hashed_password_${i}`, // 在实际应用中应存储哈希后的密码
        name: `管理员${i}`,
        enabled: true
      });
    }
    db.Admin.insertMany(adminsToInsert);
    print("Inserted 5 test documents into 'Admin'.");
  } else {
    print("Test data for 'Admin' already exists or some usernames are taken.");
  }
});

// --- Exam --- (使用 examName 作为唯一性检查的代理，实际应使用更可靠的字段)
// 注意：ID应该是唯一的，这里使用 exam_id_ 前缀保证脚本可重复运行插入不同的数据

const examIds = [];
db.Exam.countDocuments({ "examName": { $regex: /^测试考试/ } }).then(count => {
  if (count < 5) { // 如果少于5条测试考试，则插入
    const examsToInsert = [];
    for (let i = 1; i <= 5; i++) {
      const examId = `exam_id_${Date.now()}_${i}`;
      examIds.push(examId);
      examsToInsert.push({
        _id: examId, // 使用生成的唯一ID
        examName: `测试考试${i}`,
        examDescription: `这是测试考试${i}的描述。`,
        startTime: now + (i * 3600000), // 未来 i 小时开始
        duration: 120, // 120 分钟
        status: 0, // 0-未开始
        createTime: now,
        updateTime: now,
        studentCount: 0
      });
    }
    db.Exam.insertMany(examsToInsert);
    print(`Inserted ${examsToInsert.length} test documents into 'Exam'.`);
  } else {
    print("Sufficient test data for 'Exam' (based on name) already exists.");
    // 如果已存在，为了后续脚本能运行，需要获取现有的 exam IDs
    db.Exam.find({ "examName": { $regex: /^测试考试/ } }).limit(5).forEach(exam => examIds.push(exam._id));
  }

  // --- ExamSignUpLog --- (依赖于 Exam 数据)
  // 使用 examCard 作为唯一性检查
  db.ExamSignUpLog.countDocuments({ "examCard": { $regex: /^TESTCARD/ } }).then(signUpCount => {
    if (signUpCount === 0 && examIds.length > 0) {
      const examSignUpsToInsert = [];
      for (let i = 1; i <= 5; i++) {
        examSignUpsToInsert.push({
          _id: `signup_log_id_${i}`,
          examID: examIds[i % examIds.length], // 关联到已创建的考试
          examCard: `TESTCARD${i}`,
          school: `测试学校${i}`,
          idCard: `123456789012345${i}`,
          grade: `高${(i % 3) + 1}`,
          phoneNumber: `1380013800${i}`,
          name: `考生${i}`,
          guidanceTeacher: `指导老师${i}`,
          examRoom: `考场${(i % 2) + 1}`,
          examRoomNumber: `R${(i % 2) + 1}01`,
          seatNumber: `${i}`,
          examLocation: `测试地点${i}`,
          startTime: now + (i * 3600000), // 与对应考试的开始时间一致或稍作调整
          duration: 120, // 与对应考试的时长一致或稍作调整
          submitType: (i % 2) // 0 或 1
        });
      }
      db.ExamSignUpLog.insertMany(examSignUpsToInsert);
      print("Inserted 5 test documents into 'ExamSignUpLog'.");
    } else {
      if(examIds.length === 0) print("Skipping 'ExamSignUpLog' test data: No Exam IDs available.");
      else print("Test data for 'ExamSignUpLog' (based on examCard) already exists.");
    }
  });

  // --- ExamQuestionLog --- (依赖于 Exam 和 ExamSignUpLog 数据)
  // 使用 examCard 作为文档标识的一部分来检查，并关联到 ExamSignUpLog
  db.ExamQuestionLog.countDocuments({ "examCard": { $regex: /^TESTCARD/ } }).then(logCount => {
    if (logCount === 0 && examIds.length > 0) {
      const questionLogsToInsert = [];
      for (let i = 1; i <= 5; i++) {
        const studentAnswers = [];
        for (let j = 1; j <= 3; j++) { // 每个考生有3条答题记录
          studentAnswers.push({
            questionId: j,
            questionType: (j % 2), // 0主观, 1客观
            Option: (j % 2 === 1) ? ["A", "B", "C", "D"][Math.floor(Math.random() * 4)] : null, // 客观题随机答案
            score: (j % 2 === 1) ? [10, 20][Math.floor(Math.random() * 2)] : null,
            url: `http://example.com/screenshot/user${i}_q${j}.png`,
            sbmitTime: now + (i * 3600000) + (j * 60000) // 模拟答题时间
          });
        }
        questionLogsToInsert.push({
          _id: `question_log_id_${i}`,
          ExamId: examIds[i % examIds.length], // 关联到已创建的考试
          examCard: `TESTCARD${i}`, // 关联到考生报名记录
          studentAnswer: studentAnswers
        });
      }
      db.ExamQuestionLog.insertMany(questionLogsToInsert);
      print("Inserted 5 test documents into 'ExamQuestionLog'.");
    } else {
      if(examIds.length === 0) print("Skipping 'ExamQuestionLog' test data: No Exam IDs available.");
      else print("Test data for 'ExamQuestionLog' (based on examCard) already exists.");
    }
  });
});

// --- export_tasks --- (可以独立生成，或关联到 Exam)
// 使用 taskType 和 status 的组合进行粗略检查
db.export_tasks.countDocuments({ "taskType": "EXAM_SCREENSHOTS", "status": "PENDING" }).then(count => {
  if (count === 0) {
    const exportTasksToInsert = [];
    for (let i = 1; i <= 5; i++) {
      exportTasksToInsert.push({
        _id: `export_task_id_${i}`,
        // examId: examIds.length > 0 ? examIds[i % examIds.length] : `dummy_exam_id_${i}`, // 可选关联到上面创建的考试
        examId: `exam_export_id_${i}`, // 使用独立的ID，避免依赖问题，实际场景应关联
        taskType: "EXAM_SCREENSHOTS",
        status: "PENDING",
        fileUrl: null,
        fileName: null,
        errorMessage: null,
        createdAt: now - (i * 60000), // 过去 i 分钟创建
        updatedAt: now - (i * 60000)
      });
    }
    db.export_tasks.insertMany(exportTasksToInsert);
    print("Inserted 5 test documents into 'export_tasks'.");
  } else {
    print("Test data for 'export_tasks' (based on type/status) already exists.");
  }
});

print("Test data insertion script finished."); 