// 01-create-collections.js
// 此脚本在MongoDB中创建必要的集合（如果它们尚不存在）。

const collections = [
  "Admin",
  "Exam",
  "ExamQuestionLog",
  "ExamSignUpLog",
  "export_tasks"
  // 注意：Answer 实体通常作为 ExamQuestionLog 的一部分嵌入，因此不创建单独的 Answer 集合。
];

collections.forEach(collectionName => {
  const collectionExists = db.getCollectionNames().includes(collectionName);
  if (!collectionExists) {
    db.createCollection(collectionName);
    print(`Collection '${collectionName}' created.`);
  } else {
    print(`Collection '${collectionName}' already exists.`);
  }
});

// 为 Admin 集合的 username 字段创建唯一索引
const adminUsernameIndexExists = db.Admin.getIndexes().some(index => index.key && index.key.username === 1 && index.unique === true);
if (!adminUsernameIndexExists) {
  db.Admin.createIndex({ "username": 1 }, { unique: true });
  print("Unique index on 'username' for 'Admin' collection created.");
} else {
  print("Unique index on 'username' for 'Admin' collection already exists.");
}

// 为 ExamQuestionLog 集合的 ExamId 字段创建索引
const examQuestionLogExamIdIndexExists = db.ExamQuestionLog.getIndexes().some(index => index.key && index.key.ExamId === 1);
if (!examQuestionLogExamIdIndexExists) {
  db.ExamQuestionLog.createIndex({ "ExamId": 1 });
  print("Index on 'ExamId' for 'ExamQuestionLog' collection created.");
} else {
  print("Index on 'ExamId' for 'ExamQuestionLog' collection already exists.");
}

// 为 ExamQuestionLog 集合的 examCard 字段创建索引
const examQuestionLogExamCardIndexExists = db.ExamQuestionLog.getIndexes().some(index => index.key && index.key.examCard === 1);
if (!examQuestionLogExamCardIndexExists) {
  db.ExamQuestionLog.createIndex({ "examCard": 1 });
  print("Index on 'examCard' for 'ExamQuestionLog' collection created.");
} else {
  print("Index on 'examCard' for 'ExamQuestionLog' collection already exists.");
}

// 为 ExamSignUpLog 集合的 examID 字段创建索引
const examSignUpLogExamIDIndexExists = db.ExamSignUpLog.getIndexes().some(index => index.key && index.key.examID === 1);
if (!examSignUpLogExamIDIndexExists) {
  db.ExamSignUpLog.createIndex({ "examID": 1 });
  print("Index on 'examID' for 'ExamSignUpLog' collection created.");
} else {
  print("Index on 'examID' for 'ExamSignUpLog' collection already exists.");
}

// 为 ExamSignUpLog 集合的 examCard 字段创建唯一索引
const examSignUpLogExamCardIndexExists = db.ExamSignUpLog.getIndexes().some(index => index.key && index.key.examCard === 1 && index.unique === true);
if (!examSignUpLogExamCardIndexExists) {
  db.ExamSignUpLog.createIndex({ "examCard": 1 }, { unique: true });
  print("Unique index on 'examCard' for 'ExamSignUpLog' collection created.");
} else {
  print("Unique index on 'examCard' for 'ExamSignUpLog' collection already exists.");
} 