package com.zkdiman.pojo.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@ApiModel(value = "管理员登录信息")
@Builder
@Data
public class AdminLoginVO {

    @ApiModelProperty(value = "管理员唯一ID")
    private String ID; // 通常MongoDB使用String类型的ID

    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "姓名")
    private String name; // 管理员的真实姓名或昵称
}
