package com.zkdiman.pojo.vo.admin;

import lombok.Builder;
import lombok.Data;

/**
 * 导出任务状态视图对象
 */
@Data
@Builder
public class ExportTaskStatusVO {

    private String taskId;
    private String examId;
    private String taskType;
    private String status; // PENDING, PROCESSING, COMPLETED, FAILED
    private String fileUrl; // 下载链接，仅在COMPLETED状态下有效
    private String fileName;
    private String errorMessage; // 失败信息，仅在FAILED状态下有效
    private Long createdAt;
    private Long updatedAt;
} 