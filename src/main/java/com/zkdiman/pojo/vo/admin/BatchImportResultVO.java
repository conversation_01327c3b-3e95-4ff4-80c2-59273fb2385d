package com.zkdiman.pojo.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "批量导入结果")
public class BatchImportResultVO {

    @ApiModelProperty(value = "成功导入的数量")
    private int successCount;

    @ApiModelProperty(value = "失败导入的数量")
    private int failureCount;

    @ApiModelProperty(value = "导入失败的记录及原因列表")
    private List<ImportError> errors;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "导入错误详情")
    public static class ImportError {
        @ApiModelProperty(value = "Excel中的行号")
        private int rowNumber;
        @ApiModelProperty(value = "准考证号 (或其他关键标识)")
        private String identifier; // 可以是准考证号或其他唯一标识
        @ApiModelProperty(value = "错误原因")
        private String reason;
    }
} 