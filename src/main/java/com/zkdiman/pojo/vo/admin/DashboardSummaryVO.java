package com.zkdiman.pojo.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "仪表盘概要统计数据")
public class DashboardSummaryVO {

    @ApiModelProperty("学生总数")
    private Long studentCount;

    @ApiModelProperty("总考试场次数")
    private Long examCount;

    @ApiModelProperty("待开始的考试场次数")
    private Long pendingCount;

    @ApiModelProperty("进行中的考试场次数")
    private Long ongoingCount;

    @ApiModelProperty("已结束的考试场次数")
    private Long completedCount;
} 