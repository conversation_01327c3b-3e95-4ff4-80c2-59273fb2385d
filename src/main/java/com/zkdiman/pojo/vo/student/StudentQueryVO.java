package com.zkdiman.pojo.vo.student;

import com.zkdiman.pojo.entity.School;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;

@ApiModel(value = "学生查询视图对象")
@Data
public class StudentQueryVO {

    // 学生记录表唯一ID
    @ApiModelProperty(value = "学生记录表唯一ID")
    private String ID;

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "班级")
    private String className;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别，1-男，2-女")
    private Integer gender;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "准考证号/学籍号")
    @Indexed(unique = true)
    private String examCard;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "就读方式，1-走读，2-住校，3-借宿，4-其他")
    private Integer studyMode;

    @ApiModelProperty(value = "户口本性质，1-本市城镇，2-本市农业，3-非本市城镇，4-非本市农业")
    private Integer householdType;

    @ApiModelProperty(value = "成绩数组，每个元素代表一个科目的成绩，10年级(该数组存5个数据)：中考成绩（语文、数学、英语、物理、政治），7年级(该数组存3个数据)：小升初期末考试成绩（语文、数学、英语）")
    private Integer[] scores;

    @ApiModelProperty(value = "考试开始时间")
    private Long startTime;

    @ApiModelProperty(value = "考试时长，单位/分钟, 若非空, 该时间会覆盖整个组别时间")
    private Integer duration;

    @ApiModelProperty(value = "学校代码(唯一)")
    private String schoolCode; // 学校代码(唯一)

    @ApiModelProperty(value = "学校信息实体")
    private School school;
}
