package com.zkdiman.pojo.vo.student;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "任务视图对象")
@Data
public class TaskVO {

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "任务的路径")
    private String path; // 任务的路径

    @ApiModelProperty(value = "任务的基路径")
    private String basePath; // 任务的基路径

    @ApiModelProperty(value = "图片路径")
    private String image;

    @ApiModelProperty(value = "任务介绍")
    private String introduce; // 任务介绍
}
