package com.zkdiman.pojo.vo.student;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@ApiModel(value = "学生登录返回封装对象")
@Data
public class StudentLoginVo {
    @ApiModelProperty(value = "姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "准考证号")
    @NotBlank(message = "准考证号不能为空")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "准考证号只能包含字母和数字")
    private String examCard;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Long startDate;

    @ApiModelProperty(value = "学校名称")
    @NotBlank(message = "学校不能为空")
    private String school;

    @ApiModelProperty(value ="考试持续时间")
    @NotNull(message = "考试持续时间不能为空(单位为分钟)")
    private Integer Duration;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不能为空")
    private Long endDate;

    @ApiModelProperty(value ="距离考试结束剩余时间")
    private Long remainEndTime;

    @ApiModelProperty(value ="距离考试开始剩余时间")
    private Long remainStartTime;

    @ApiModelProperty(value = "考试任务列表")
    private List<TaskVO> taskList; // 考试任务列表，包含任务名称、路径等信息
}
