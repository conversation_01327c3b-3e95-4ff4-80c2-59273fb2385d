package com.zkdiman.pojo.vo.student;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 统一答题请求数据接口
 */
@ApiModel(value = "统一答题请求数据接口")
@Data
public class AnswerSubmitVO {
    
    /**
     * 试题ID
     */
    private String questionId;
    
    /**
     * 考场ID
     */
    private String competitionId;
    
    /**
     * 试题类型
     */
    private String questionType;
    
    /**
     * 用户答案（格式根据题型不同而不同）
     */
    private Object userAnswer;
}