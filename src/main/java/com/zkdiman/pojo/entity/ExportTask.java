package com.zkdiman.pojo.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 异步导出任务实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "export_tasks") // 指定MongoDB中的集合名称
public class ExportTask {

    @Id
    private String id; // 任务ID，自动生成或手动指定

    private String examId; // 关联的考试场次ID

    private String taskType; // 任务类型，例如 "EXAM_SCREENSHOTS"

    private String status; // 任务状态: PENDING (待处理), PROCESSING (处理中), COMPLETED (已完成), FAILED (失败)

    private String fileUrl; // 任务完成后，文件的公网访问URL（如腾讯云COS）

    private String fileName; // 导出的文件名，例如 "exam_name.zip"

    private String errorMessage; // 如果任务失败，记录错误信息

    private Long createdAt; // 任务创建时间戳

    private Long updatedAt; // 任务最后更新时间戳
} 