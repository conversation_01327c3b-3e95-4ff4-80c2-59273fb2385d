package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document("School")
@ApiModel(value = "学校实体类")
public class School {
    @Id
    @ApiModelProperty(value = "学校唯一ID")
    private String ID; // 学校唯一ID，对应数据库中的主键_id

    @ApiModelProperty(value = "学校名称")
    private String schoolName; // 学校名称

    @ApiModelProperty(value = "学校区名")
    private String zoneName; // 区名

    @ApiModelProperty(value = "学校代码(唯一)")
    private String schoolCode; // 学校代码(唯一)

    @ApiModelProperty(value = "本科科学教师数")
    private String teacherNumber; // 本科科学教师数

    @ApiModelProperty(value = "学校负责人员的联系电话(办公电话及手机)")
    private String contactNumber; // 学校负责人员的联系电话(办公电话及手机)
}
