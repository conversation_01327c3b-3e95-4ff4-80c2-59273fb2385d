package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@ApiModel("答案实体类")
@Data
public class Answer {
    /**
     * 题目题号,第几题
     */
    @NotBlank(message = "题号不能为空")
    @ApiModelProperty(value = "题目题号,第几题")
    private String questionId;

    @ApiModelProperty(value = "题目类型:0主观题,1客观题")
    private Integer questionType;

    /**
     * 选择题(客观题)答案：A、B、C、D
     */
    @NotBlank(message = "答案不能为空")
    @ApiModelProperty(value = "选择题(此属性客观题才有值)答案：A、B、C、D")
    private String Option;

    /**
     * 题目分数
     */
    @ApiModelProperty(value = "题目分数")
    private Integer score;

    /**
     * 屏幕截图url
     */
    @NotBlank(message = "截图URL不能为空")
    @URL(message = "截图URL格式不正确") // Basic URL format validation
    @ApiModelProperty(value = "答题截图的URL", required = true)
    private String url;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间,单位：毫秒制时间戳")
    @NotBlank(message = "提交时间不能为空")
    private Long sbmitTime;

    /**
     *作答记录文本部分
     */
    @ApiModelProperty(value = "作答记录文本部分,key为小题号2_1_1(可补充类似支持部分，反对部分帮助理解)，值为文本正文")
    private Map<String,String> text;
}
