package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 考试任务实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamTask {

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "任务的路径")
    private String path; // 任务的路径

    @ApiModelProperty(value = "任务的基路径")
    private String basePath; // 任务的基路径

    @ApiModelProperty(value = "图片路径")
    private String image;

    @ApiModelProperty(value = "任务介绍")
    private String introduce; // 任务介绍

    @ApiModelProperty(value = "任务类型")
    private String type; // 任务类型:0-初中任务,1-高中任务
}
