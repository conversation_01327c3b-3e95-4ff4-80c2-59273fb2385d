package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("考试试题作答记录")
@Document("CompetitionQuestionLog")
public class CompetitionQuestionLog {
    @Id
    private String id;

    /**
     * 比赛id（暂定考试是一次性的，所以不需要）（后期可能需要，如果一个学生参加多长考试，可以用来关联考场,后期如果用到这个字段，那么在学生在提交答题记录时，额外还需要提交，比赛的id）
     */
    @ApiModelProperty(value = "比赛id")
    @Indexed
    private String ExamId;

    /**
     * 准考证号
     */
    @ApiModelProperty(value = "准考证号")
    @Indexed
    private String examCard;


    /**
     * 屏幕截图集合
     */
    @ApiModelProperty(value = "屏幕截图集合，key试题类型:0主观题,1客观题;value答案集合")
    private ArrayList<Answer> studentAnswer;

    /**
     * 任务列表集合
     */
    @ApiModelProperty(value = "任务列表集合")
    private List<ExamTask> taskList;


}


