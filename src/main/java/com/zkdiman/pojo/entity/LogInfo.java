package com.zkdiman.pojo.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 日志信息实体类
 */
@Data
public class LogInfo {
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 请求参数
     */
    private Object[] args;
    
    /**
     * 返回结果
     */
    private Object result;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTime;
    
    /**
     * 日志描述
     */
    private String description;
    
    /**
     * 记录时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 是否执行成功
     */
    private boolean success;
    
    /**
     * 异常信息
     */
    private String errorMessage;
}