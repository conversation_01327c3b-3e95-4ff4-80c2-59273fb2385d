package com.zkdiman.pojo.entity;

import com.zkdiman.pojo.vo.student.TaskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document("CompetitionSignUpLog")
@ApiModel(value = "考生考试报名记录表")
public class CompetitionSignUpLog {

    /**
     * 考生考试报名记录表
     * 该表用于记录考生的考试报名信息，包括准考证号、年级、班级、姓名等信息。
     * 该表的每条记录对应一个考生的考试报名信息。
     */

    // 记录唯一ID
    @Id
    @ApiModelProperty(value = "记录唯一ID")
    private String ID;

    // 考试唯一ID，用来关联考试信息
    @ApiModelProperty(value = "考试唯一ID")
    @Indexed
    private String examID;


    // 学校ID，用来关联学校信息
    @ApiModelProperty(value = "学校代码(唯一),用于关联学校信息")
    private String schoolCode;

    // 冗余字段，避免多表查询，提高查询效率
    @ApiModelProperty(value = "学校名称")
    private String school;


    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "班级")
    private String className;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别，1-男，2-女")
    private Integer gender;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "准考证号/学籍号")
    @Indexed(unique = true)
    private String examCard;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "就读方式，1-走读，2-住校，3-借宿，4-其他")
    private Integer studyMode;

    @ApiModelProperty(value = "户口本性质，1-本市城镇，2-本市农业，3-非本市城镇，4-非本市农业")
    private Integer householdType;



    @ApiModelProperty(value = "成绩数组，每个元素代表一个科目的成绩，10年级(该数组存5个数据)：中考成绩（语文、数学、英语、物理、政治），7年级(该数组存3个数据)：小升初期末考试成绩（语文、数学、英语）")
    private Integer[] scores;



    // 考试相关信息
    @ApiModelProperty(value = "考场")
    private String examRoom;

    @ApiModelProperty(value = "考场号")
    private String examRoomNumber;

    @ApiModelProperty(value = "座位号")
    private String seatNumber;

    @ApiModelProperty(value = "考试地点")
    private String examLocation;

    @ApiModelProperty(value = "考试开始时间")
    private Long startTime;

    @ApiModelProperty(value = "考试时长，单位/分钟, 若非空, 该时间会覆盖整个组别时间")
    private Integer duration;

    @ApiModelProperty(value = "提交考试类型,如果submitType为1，则代表按主动提交，否则代表时间用尽自动提交考试")//自定义字段
    private Integer submitType;


}
