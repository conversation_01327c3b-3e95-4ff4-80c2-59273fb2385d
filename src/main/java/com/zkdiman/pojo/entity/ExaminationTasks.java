package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 考试任务实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "ExaminationTasks") // 指定MongoDB中的集合名称
public class ExaminationTasks {
    @Id
    private String id; // 任务ID，自动生成或手动指定

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "任务的路径")
    private String path; // 任务的路径

    @ApiModelProperty(value = "任务的基路径")
    private String basePath; // 任务的基路径

    @ApiModelProperty(value = "图片路径")
    private String image;

    @ApiModelProperty(value = "任务介绍")
    private String introduce; // 任务介绍

    @ApiModelProperty(value = "任务类型")
    private String type; // 任务类型:0-初中任务,1-高中任务
}
