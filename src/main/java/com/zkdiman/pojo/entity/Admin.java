package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document("Admin") // 指定MongoDB集合名称
@ApiModel(value = "管理员信息")
public class Admin {

    @Id
    @ApiModelProperty(value = "管理员唯一ID")
    private String ID; // 通常MongoDB使用String类型的ID

    @ApiModelProperty(value = "用户名", required = true)
    @Indexed(unique = true) // 用户名唯一索引
    private String username;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "姓名")
    private String name; // 管理员的真实姓名或昵称

     @ApiModelProperty(value = "是否启用")
     private Boolean enabled;

} 