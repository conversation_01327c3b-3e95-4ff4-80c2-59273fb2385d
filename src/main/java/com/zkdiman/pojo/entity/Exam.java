package com.zkdiman.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document("Exam")
@ApiModel(value = "考试实体类")
public class Exam {

    @Id
    @ApiModelProperty(value = "考试唯一ID")//考试场次id
    private String ID;

    @ApiModelProperty(value = "考试名称")//考试场次名称
    private String examName;

    @ApiModelProperty(value = "考试内容描述")
    private String examDescription;

    @ApiModelProperty(value = "考试开始时间,为考场添加学生时会把这个值给到学生")
    private Long startTime;

    @ApiModelProperty(value = "考试时长，单位/分钟,为考场添加学生时会把这个值给到学生")
    private Integer duration;

    @ApiModelProperty(value = "考试状态，0-未开始，1-进行中，2-已结束，3-已下架")
    private Integer status;

    @ApiModelProperty(value = "考试创建时间")
    private Long createTime=new Date().getTime();

    @ApiModelProperty(value = "考试更新时间")
    private Long updateTime;

    @ApiModelProperty(value = "学生数量")
    private Integer studentCount;
}
