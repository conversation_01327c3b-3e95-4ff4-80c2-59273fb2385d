package com.zkdiman.pojo.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

// For create, username and password will be validated in service layer
// For update, all fields are optional

@Data
@ApiModel(description = "新增/修改管理员信息的请求参数")
public class AdminSaveDTO {

    @ApiModelProperty(value = "用户名 (新增时必须; 修改时若提供需注意唯一性)", example = "newUser")
    private String username;

    @ApiModelProperty(value = "密码 (新增时必须; 修改时可选，不修改则不传)", example = "newPassword123")
    private String password;

    @ApiModelProperty(value = "姓名", example = "新管理员")
    private String name;

    @ApiModelProperty(value = "是否启用 (新增时默认为true)", example = "true")
    private Boolean enabled;
} 