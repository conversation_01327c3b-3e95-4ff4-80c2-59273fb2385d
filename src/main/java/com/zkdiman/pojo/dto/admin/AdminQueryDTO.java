package com.zkdiman.pojo.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

@Data
@ApiModel(description = "管理员列表查询的请求参数")
public class AdminQueryDTO {

    @ApiModelProperty(value = "管理员用户名", example = "admin_user")
    private String username;

    @ApiModelProperty(value = "管理员姓名（模糊查询）", example = "张三")
    private String name;

    @ApiModelProperty(value = "是否启用 (true: 已启用, false: 已禁用)", example = "true")
    private Boolean enabled;

    @ApiModelProperty(value = "分页查询的页码，默认为1", example = "1", required = true)
    @Min(value = 1, message = "页码最小为1")
    private Integer page = 1;

    @ApiModelProperty(value = "分页查询的每页记录数，默认为10", example = "10", required = true)
    @Min(value = 1, message = "每页记录数最小为1")
    private Integer pageSize = 10;
} 