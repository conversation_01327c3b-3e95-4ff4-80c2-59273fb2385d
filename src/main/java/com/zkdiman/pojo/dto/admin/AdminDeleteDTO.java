package com.zkdiman.pojo.dto.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(description = "批量删除管理员的请求参数")
public class AdminDeleteDTO {

    @ApiModelProperty(value = "要删除的管理员ID列表", required = true)
    @NotEmpty(message = "管理员ID列表不能为空")
    private List<String> adminIds;

    @ApiModelProperty(value = "当前操作的管理员ID", required = true)
    @NotEmpty(message = "当前操作的管理员ID不能为空")
     private String currentAdminId;
} 