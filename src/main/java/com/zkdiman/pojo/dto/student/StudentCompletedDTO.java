package com.zkdiman.pojo.dto.student;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "学生交卷请求体")
public class StudentCompletedDTO {
    @NotBlank(message = "准考证号不能为空")
    @ApiModelProperty(value = "准考证号", required = true)
    private String examCard;

    @NotNull(message = "提交类型不能为空")
    @ApiModelProperty(value = "提交类型 (0: 未提交/自动提交, 1: 主动提交)", required = true, example = "1")
    private Integer submitType;
} 