package com.zkdiman.pojo.dto.student;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

@Data
@ApiModel(description = "学生考试时间调整DTO")
public class StudentTimeAdjustmentDTO {

    @ApiModelProperty(value = "学生个人的考试开始时间 (Epoch毫秒)", example = "1730005000000")
    private Long startTime;

    @ApiModelProperty(value = "学生个人的考试时长 (分钟)", example = "100")
    @Min(value = 1, message = "考试时长必须大于0")
    private Integer duration;
} 