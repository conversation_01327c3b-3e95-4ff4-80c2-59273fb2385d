package com.zkdiman.pojo.dto.student;

import com.zkdiman.pojo.entity.Answer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "学生提交答题截图URL请求体")
public class StudentScreenshotSubmitDTO {

    @NotBlank(message = "准考证号不能为空")
    @ApiModelProperty(value = "准考证号", required = true)
    private String examCard;

    //实体类里已经校验过了
    private Answer answer;
} 