package com.zkdiman.pojo.dto.student;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ApiModel(value = "学生分页查询数据传输对象")
@Data
public class StudentPageDTO {
    @ApiModelProperty(value = "准考证号")
    private String examCard;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "学校")
    private String school;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer page;

    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @ApiModelProperty(value = "每页记录数", required = true, example = "10")
    private Integer pageSize;
}
