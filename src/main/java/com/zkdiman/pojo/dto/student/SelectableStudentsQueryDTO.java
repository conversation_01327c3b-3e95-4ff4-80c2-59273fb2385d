package com.zkdiman.pojo.dto.student;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

@Data
@ApiModel(description = "查询可选学员列表的请求参数")
public class SelectableStudentsQueryDTO {

    @ApiModelProperty(value = "准考证号（精确查询）", example = "123456789")
    private String examCard;

    @ApiModelProperty(value = "学生姓名（模糊查询）", example = "张三")
    private String name;

    @ApiModelProperty(value = "学校（模糊查询）", example = "示例中学")
    private String school;

    @ApiModelProperty(value = "需要排除的考试场次ID. 如果提供此参数，则返回未分配到此场次的学生", example = "examIdToExclude")
    private String excludeExamId;

    @ApiModelProperty(value = "分页查询的页码，默认为1", example = "1", required = true)
    @Min(value = 1, message = "页码最小为1")
    private Integer page = 1;

    @ApiModelProperty(value = "分页查询的每页记录数，默认为10", example = "10", required = true)
    @Min(value = 1, message = "每页记录数最小为1")
    private Integer pageSize = 10;
} 