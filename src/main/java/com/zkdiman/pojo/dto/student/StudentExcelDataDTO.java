package com.zkdiman.pojo.dto.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;

@Data
public class StudentExcelDataDTO {

    private int excelRowNumber; // Excel中的原始行号，用于错误报告
    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "班级")
    private String className;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别，1-男，2-女")
    private Integer gender;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "准考证号/学籍号")
    @Indexed(unique = true)
    private String examCard;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "就读方式，1-走读，2-住校，3-借宿，4-其他")
    private Integer studyMode;

    @ApiModelProperty(value = "户口本性质，1-本市城镇，2-本市农业，3-非本市城镇，4-非本市农业")
    private Integer householdType;

    // 冗余字段，避免多表查询，提高查询效率
    @ApiModelProperty(value = "学校名称")
    private String school;

    @ApiModelProperty(value = "学校区名")
    private String zoneName; // 区名

    @ApiModelProperty(value = "学校代码(唯一)")
    private String schoolCode; // 学校代码(唯一)

    @ApiModelProperty(value = "本科科学教师数")
    private String teacherNumber; // 本科科学教师数

    @ApiModelProperty(value = "学校负责人员的联系电话(办公电话及手机)")
    private String contactNumber; // 学校负责人员的联系电话(办公电话及手机)


    // 考试相关信息
    @ApiModelProperty(value = "考场")
    private String examRoom;

    @ApiModelProperty(value = "考场号")
    private String examRoomNumber;

    @ApiModelProperty(value = "座位号")
    private String seatNumber;

    @ApiModelProperty(value = "考试地点")
    private String examLocation;

    @ApiModelProperty(value = "考试开始时间")
    private Long startTime;

    @ApiModelProperty(value = "考试时长，单位/分钟, 若非空, 该时间会覆盖整个组别时间")
    private Integer duration;

    @ApiModelProperty(value = "提交考试类型,如果submitType为1，则代表按主动提交，否则代表时间用尽自动提交考试")//自定义字段
    private Integer submitType;


    // 成绩相关信息
    @ApiModelProperty(value = "成绩数组，每个元素代表一个科目的成绩，10年级(该数组存5个数据)：中考成绩（语文、数学、英语、物理、政治），7年级(该数组存3个数据)：小升初期末考试成绩（语文、数学、英语）")
    private Integer[] scores;
} 