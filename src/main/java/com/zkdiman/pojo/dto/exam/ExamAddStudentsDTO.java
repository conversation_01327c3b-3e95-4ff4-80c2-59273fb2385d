package com.zkdiman.pojo.dto.exam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(description = "为考试场次批量添加学员的请求参数")
public class ExamAddStudentsDTO {

    @ApiModelProperty(value = "要添加的学生记录ID列表 (CompetitionSignUpLog的主键)", required = true)
    @NotEmpty(message = "学生ID列表不能为空")
    private List<String> studentIds;

    @ApiModelProperty(value = "考场", example = "A101", required = false)
    private String examRoom;

    @ApiModelProperty(value = "座位号", example = "32", required = false)
    private String seatNumber;

    @ApiModelProperty(value = "考试开始时间(毫秒制时间戳)", example = "1730000000000", required = true)
    @NotNull(message = "考试开始时间不能为空")
    private Long startTime;

    @ApiModelProperty(value = "考试时长，单位/分钟", example = "60", required = true)
    @NotNull(message = "考试时长不能为空")
    private Integer duration;
} 