package com.zkdiman.pojo.dto.exam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "考试添加数据传输对象")
@Data
public class ExamAddDTO {
    @ApiModelProperty(value = "考试名称")//考试场次名称
    private String examName;

    @ApiModelProperty(value = "考试内容描述")
    private String examDescription;

    @ApiModelProperty(value = "考试开始时间,为考场添加学生时会把这个值给到学生")
    private Long startTime;

    @ApiModelProperty(value = "考试时长，单位/分钟,为考场添加学生时会把这个值给到学生")
    private Integer duration;

    @ApiModelProperty(value = "考试状态，0-未开始，1-进行中，2-已结束，3-已下架")
    private Integer status;
}
