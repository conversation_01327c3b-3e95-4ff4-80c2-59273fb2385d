package com.zkdiman.pojo.dto.exam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "考场更新数据传输对象")
@Data
public class ExamUpdateDTO {

    @ApiModelProperty(value = "新的考试名称", required = false)
    private String examName;

    @ApiModelProperty(value = "新的考试描述", required = false)
    private String examDescription;

    @ApiModelProperty(value = "新的开始时间 (Epoch毫秒)", required = false)
    private Long startTime;

    @ApiModelProperty(value = "新的考试时长 (分钟)", required = false)
    private Integer duration;

    @ApiModelProperty(value = "新的状态 (0: 未开始, 1: 进行中/已上架, 2: 已结束, 3: 已下架)", required = false)
    private Integer status;
} 