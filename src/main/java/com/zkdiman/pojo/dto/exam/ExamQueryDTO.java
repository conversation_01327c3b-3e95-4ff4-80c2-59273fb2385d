package com.zkdiman.pojo.dto.exam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ApiModel(value = "考场查询数据传输对象")
@Data
public class ExamQueryDTO {
    @ApiModelProperty(value = "考试名称")//考试场次名称
    private String examName;

    @ApiModelProperty(value = "考试状态，0-未开始，1-进行中，2-已结束，3-已下架")
    private Integer status;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer page;

    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @ApiModelProperty(value = "每页记录数", required = true, example = "10")
    private Integer pageSize;
}
