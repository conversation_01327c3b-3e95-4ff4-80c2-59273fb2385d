package com.zkdiman.pojo.dto.exam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

@Data
@ApiModel(description = "查询考试场次下学员列表的请求参数")
public class ExamStudentQueryDTO {

    @ApiModelProperty(value = "准考证号（精确查询）", example = "123456789")
    private String examCard;

    @ApiModelProperty(value = "学生姓名（模糊查询）", example = "张三")
    private String name;

    @ApiModelProperty(value = "学校（模糊查询）", example = "示例中学")
    private String school;

    @ApiModelProperty(value = "分页查询的页码，默认为1", example = "1", required = true)
    @Min(value = 1, message = "页码最小为1")
    private Integer page = 1;

    @ApiModelProperty(value = "分页查询的每页记录数，默认为10", example = "10", required = true)
    @Min(value = 1, message = "每页记录数最小为1")
    private Integer pageSize = 10;

    // The 'id' (examId) will be a path variable in the controller, so it's not needed here.
} 