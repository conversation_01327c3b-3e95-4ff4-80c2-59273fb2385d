package com.zkdiman.pojo.dto.exam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(description = "从考试场次批量移除学员的请求参数")
public class ExamRemoveStudentsDTO {

    @ApiModelProperty(value = "要移除的学生记录ID列表 (CompetitionSignUpLog的主键)", required = true)
    @NotEmpty(message = "学生ID列表不能为空")
    private List<String> studentIds;
} 