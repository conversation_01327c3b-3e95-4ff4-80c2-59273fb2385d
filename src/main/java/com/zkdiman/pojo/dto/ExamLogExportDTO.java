package com.zkdiman.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;

import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;



/**
 * 考试答题记录导出DTO (最终版)
 * 这个类的结构是固定的，因为它定义了我们期望的Excel输出格式。
 * 它的数据将从多个MongoDB集合(CompetitionQuestionLog, CompetitionSignUpLog等)中聚合而来。
 */
// @ColumnWidth(25) // 可以设置全局列宽
// @HeadRowHeight(20) // 可以设置表头高度
// @ContentRowHeight(18) // 可以设置内容行高度
@Data
public class ExamLogExportDTO {

    @ExcelProperty(value = "区名", index = 0)
    @ColumnWidth(15)
    private String zoneName;

    @ExcelProperty(value = "学校", index = 1)
    @ColumnWidth(20)
    private String school;

    @ExcelProperty(value = "班级", index = 2)
    @ColumnWidth(10)
    private String className;

    @ExcelProperty(value = "学生姓名", index = 3)
    @ColumnWidth(15)
    private String studentName;

    @ExcelProperty(value = "准考证号", index = 4)
    @ColumnWidth(20)
    private String examCard;

    @ExcelProperty(value = "任务名称", index = 5)
    @ColumnWidth(15)
    private String taskName;

    @ExcelProperty(value = "题号", index = 6)
    @ColumnWidth(10)
    private String questionId; // 题号可能是"2-1"这样的字符串，所以类型改为String

    @ExcelProperty(value = "题目类型", index = 7)
    @ColumnWidth(15)
    private String questionType;

    @ExcelProperty(value = "选项", index = 8)
    @ColumnWidth(15)
    private String option;

    @ExcelProperty(value = "答题截图", index = 9)
    @ColumnWidth(25)
    private URL answerImageUrl;

    @ExcelProperty(value = "文本答案", index = 10)
    @ColumnWidth(30)
    private String textAnswer;

    @ExcelProperty(value = "提交时间", index = 11)
    @ColumnWidth(25)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String submitTime;
}