package com.zkdiman.common.enums;

import lombok.Getter;

/**
 * 互动类型枚举类
 */
@Getter
public enum InteractionTypeEnum {
    
    CLICK("click", "点击"),
    DRAG("drag", "拖拽"),
    CONNECT("connect", "连线"),
    SORT("sort", "排序"),
    INPUT("input", "输入");
    
    private final String code;//编码
    private final String desc;//描述

    /**
     * 构造方法
     * @param code
     * @param desc
     */
    InteractionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举
     * @param code
     * @return
     */
    public static InteractionTypeEnum getByCode(String code) {
        for (InteractionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}