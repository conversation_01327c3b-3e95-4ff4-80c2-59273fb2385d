package com.zkdiman.common.enums;

import lombok.Getter;

/**
 * 题型枚举类
 */
@Getter
public enum QuestionTypeEnum {
    
    CHOICE("1", "选择题"),
    JUDGE("2", "判断题"),
    DRAG("3", "拖拽题"),
    FILL_BLANK("4", "填空题"),
    CONNECT("5", "连线题"),
    TEXT("6", "文字题"),
    SORT("7", "排序题"),
    GRAPHIC("8", "图形题");
    
    private final String code;
    private final String desc;
    
    QuestionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static QuestionTypeEnum getByCode(String code) {
        for (QuestionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}