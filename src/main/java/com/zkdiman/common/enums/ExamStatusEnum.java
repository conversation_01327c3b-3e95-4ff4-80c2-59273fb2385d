package com.zkdiman.common.enums;

import lombok.Getter;

/**
 * 考试状态枚举
 */
@Getter
public enum ExamStatusEnum {

    PENDING(0, "未开始"),
    ONGOING(1, "进行中"),
    COMPLETED(2, "已结束"),
    CLOSED(3, "已下架"); // 根据之前的DTO/Entity定义，似乎还有个已下架状态

    private final int status;
    private final String description;

    ExamStatusEnum(int status, String description) {
        this.status = status;
        this.description = description;
    }

    // Optional: 方法根据 status code 获取枚举实例
    public static ExamStatusEnum fromStatus(int status) {
        for (ExamStatusEnum value : ExamStatusEnum.values()) {
            if (value.status == status) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid ExamStatus status: " + status);
    }
} 