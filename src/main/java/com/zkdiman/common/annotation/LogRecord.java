package com.zkdiman.common.annotation;

import java.lang.annotation.*;

/**
 * 日志记录注解
 * 用于标记需要记录日志的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogRecord {
    
    /**
     * 日志描述信息
     */
    String value() default "";
    
    /**
     * 是否记录请求参数
     */
    boolean logArgs() default true;
    
    /**
     * 是否记录返回结果
     */
    boolean logResult() default true;
    
    /**
     * 是否记录执行时间
     */
    boolean logExecutionTime() default true;
}