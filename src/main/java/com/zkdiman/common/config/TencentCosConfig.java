package com.zkdiman.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 腾讯云COS配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "tencent.cos")
public class TencentCosConfig {
    
    /**
     * 腾讯云API密钥ID
     */
    private String secretId = "AKIDdIyXv9FGAtQildGikaDxZ2JNemkW4XzP";
    
    /**
     * 腾讯云API密钥Key
     */
    private String secretKey = "LRM2U9XjfMpS4eO6pOo0xgCZ3Qds9PTT";
    
    /**
     * 存储桶名称
     */
    private String bucketName = "iai-1311740348";
    
    /**
     * 地域信息
     */
    private String region = "ap-beijing";
    
    /**
     * 导出文件存储文件夹
     */
    private String exportFolder = "exports";
    
    /**
     * CDN域名（可选）
     */
    private String cdnDomain;
}
