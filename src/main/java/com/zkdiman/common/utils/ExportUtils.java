package com.zkdiman.common.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.entity.ExportTask;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.mongodb.core.query.Criteria;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 导出工具类
 * 使用Hutool、OkHttp3、Apache Commons Compress简化文件导出操作
 */
public class ExportUtils {

    private static final Logger logger = LogManager.getLogger(ExportUtils.class);
    private static final int CONNECTION_TIMEOUT = 10; // 10秒
    private static final int READ_TIMEOUT = 30; // 30秒

    private static MongoDBUtils mongoDBUtils;

    // OkHttp客户端，复用连接池
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    // 静态初始化MongoDB工具类
    public static void setMongoDBUtils(MongoDBUtils mongoDBUtils) {
        ExportUtils.mongoDBUtils = mongoDBUtils;
    }
    
    /**
     * 使用OkHttp3从URL下载图片数据
     *
     * @param imageUrl 图片URL
     * @return 图片字节数组，下载失败返回null
     */
    public static byte[] downloadImageFromUrl(String imageUrl) {
        if (StrUtil.isBlank(imageUrl)) {
            logger.warn("图片URL为空，跳过下载");
            return null;
        }

        try {
            Request request = new Request.Builder()
                    .url(imageUrl)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .build();

            try (Response response = HTTP_CLIENT.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    byte[] bytes = response.body().bytes();
                    logger.debug("成功下载图片，URL: {}, 大小: {} bytes", imageUrl, bytes.length);
                    return bytes;
                } else {
                    logger.warn("下载图片失败，HTTP状态码: {} for URL: {}", response.code(), imageUrl);
                    return null;
                }
            }
        } catch (Exception e) {
            logger.error("下载图片异常，URL: {}, 错误: {}", imageUrl, e.getMessage());
            return null;
        }
    }
    
    /**
     * 使用Hutool从URL中提取文件名
     *
     * @param fileUrl 文件URL
     * @return 文件名，提取失败返回默认名称
     */
    public static String extractFileNameFromUrl(String fileUrl) {
        if (StrUtil.isBlank(fileUrl)) {
            return "screenshot.png";
        }

        try {
            String fileName = URLUtil.getPath(fileUrl);
            if (StrUtil.isNotBlank(fileName)) {
                // 移除路径分隔符，只保留文件名
                fileName = fileName.substring(fileName.lastIndexOf('/') + 1);
                // 移除查询参数
                if (fileName.contains("?")) {
                    fileName = fileName.substring(0, fileName.indexOf("?"));
                }
                return StrUtil.isNotBlank(fileName) ? fileName : "screenshot.png";
            }
            return "screenshot.png";
        } catch (Exception e) {
            logger.warn("无法从URL提取文件名: {}, 错误: {}", fileUrl, e.getMessage());
            return "screenshot.png";
        }
    }
    
    /**
     * 使用Hutool创建安全的文件名（移除特殊字符）
     *
     * @param originalName 原始文件名
     * @param defaultName 默认文件名
     * @return 安全的文件名
     */
    public static String createSafeFileName(String originalName, String defaultName) {
        if (StrUtil.isBlank(originalName)) {
            return defaultName;
        }
        return originalName.replaceAll("[^a-zA-Z0-9\u4E00-\u9FA5_.-]", "_");
    }
    
    /**
     * 使用Apache Commons Compress向ZIP输出流添加文本文件
     *
     * @param zos ZIP输出流
     * @param fileName 文件名
     * @param content 文件内容
     * @throws IOException IO异常
     */
    public static void addTextFileToZip(ZipArchiveOutputStream zos, String fileName, String content) throws IOException {
        ZipArchiveEntry entry = new ZipArchiveEntry(fileName);
        entry.setSize(content.getBytes(StandardCharsets.UTF_8).length);
        zos.putArchiveEntry(entry);
        IoUtil.write(zos, false, content.getBytes(StandardCharsets.UTF_8));
        zos.closeArchiveEntry();
    }

    /**
     * 使用Apache Commons Compress向ZIP输出流添加图片文件
     *
     * @param zos ZIP输出流
     * @param fileName 文件名
     * @param imageBytes 图片字节数组
     * @throws IOException IO异常
     */
    public static void addImageFileToZip(ZipArchiveOutputStream zos, String fileName, byte[] imageBytes) throws IOException {
        if (imageBytes != null && imageBytes.length > 0) {
            ZipArchiveEntry entry = new ZipArchiveEntry(fileName);
            entry.setSize(imageBytes.length);
            zos.putArchiveEntry(entry);
            IoUtil.write(zos, false, imageBytes);
            zos.closeArchiveEntry();
        }
    }
    
    /**
     * 更新导出任务状态
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @param fileUrl 文件URL（可选）
     * @param fileName 文件名（可选）
     * @param errorMessage 错误信息（可选）
     */
    public static void updateTaskStatus(String taskId, String status, String fileUrl, String fileName, String errorMessage) {
        if (mongoDBUtils == null) {
            logger.error("MongoDBUtils未初始化，无法更新任务状态");
            return;
        }
        
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("status", status);
        updateMap.put("updatedAt", System.currentTimeMillis());

        Optional.ofNullable(fileUrl).ifPresent(url -> updateMap.put("fileUrl", url));
        Optional.ofNullable(fileName).ifPresent(name -> updateMap.put("fileName", name));
        Optional.ofNullable(errorMessage).ifPresent(error -> updateMap.put("errorMessage", error));
        
        boolean updated = mongoDBUtils.updateOne(Criteria.where("id").is(taskId), updateMap, ExportTask.class);
        if (updated) {
            logger.info("导出任务 {} 状态已更新为: {}", taskId, status);
        } else {
            logger.warn("导出任务 {} 状态更新失败 (可能任务不存在或无变化)", taskId);
        }
    }

    /**
     * 分析答题记录中的URL统计信息
     *
     * @param answers 答题记录列表
     * @return URL统计信息字符串
     */
    public static String analyzeUrlStatistics(java.util.List<?> answers) {
        if (answers == null || answers.isEmpty()) {
            return "无答题记录";
        }

        java.util.Map<String, Integer> urlCount = new java.util.HashMap<>();
        int totalAnswers = answers.size();
        int validUrls = 0;

        for (Object answer : answers) {
            if (answer instanceof Answer) {
                Answer ans = (Answer) answer;
                if (ans.getUrl() != null && !ans.getUrl().trim().isEmpty()) {
                    validUrls++;
                    urlCount.put(ans.getUrl(), urlCount.getOrDefault(ans.getUrl(), 0) + 1);
                }
            }
        }

        int uniqueUrls = urlCount.size();
        long duplicateUrls = urlCount.values().stream().mapToInt(Integer::intValue).sum() - uniqueUrls;

        return String.format("总答题记录: %d, 有效URL: %d, 唯一URL: %d, 重复URL: %d",
                           totalAnswers, validUrls, uniqueUrls, duplicateUrls);
    }
}
