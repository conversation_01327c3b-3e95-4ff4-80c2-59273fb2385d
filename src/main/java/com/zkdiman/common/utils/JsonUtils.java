package com.zkdiman.common.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;


public class JsonUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.findAndRegisterModules();
    }

    /**
     * 从JSON字符串中提取指定字段的值，如果不存在或解析错误则返回默认值
     * @param json JSON字符串
     * @param fieldName 字段名
     * @param defaultValue 默认值，当字段不存在或解析错误时返回
     * @return 字段值或默认值
     */
    public static String getStringField(String json, String fieldName, String defaultValue) {
        try {
            JsonNode node = objectMapper.readTree(json);
            return node.has(fieldName) ? node.get(fieldName).asText() : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
}
