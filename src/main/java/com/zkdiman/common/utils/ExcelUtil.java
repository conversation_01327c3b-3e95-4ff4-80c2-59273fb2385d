package com.zkdiman.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 通用Excel导出工具类 (基于EasyExcel)
 * 封装了通用的导出逻辑，与具体业务解耦。
 */
@Slf4j
public class ExcelUtil {

    /**
     * 将数据列表导出为Excel文件并直接通过HTTP响应下载
     *
     * @param response  HttpServletResponse对象，用于设置响应头和获取输出流
     * @param fileName  导出的文件名 (例如: "学生答题记录.xlsx")
     * @param sheetName Excel工作表的名称 (例如: "答题记录")
     * @param dataList  要导出的数据列表，列表中的对象类型应与clazz对应
     * @param clazz     数据对象的Class类型，EasyExcel会根据此类的注解来生成表头和数据
     * @param <T>       泛型参数，代表任意的DTO类型
     */
    public static <T> void export(HttpServletResponse response, String fileName, String sheetName, List<T> dataList, Class<T> clazz) {
        try {
            // 1. 设置响应头，告知浏览器这是一个需要下载的文件
            // Content-Type 指定了文件的MIME类型
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());

            // 2. 对文件名进行URL编码，以防止中文文件名乱码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            // 3. Content-Disposition 头指定了文件名和下载方式
            // attachment; 表示作为附件下载
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName);

            // 4. 使用EasyExcel将数据写入到response的输出流
            // .write(response.getOutputStream(), clazz) 指定了输出目标和数据模型
            // .sheet(sheetName) 指定了工作表名称
            // .registerWriteHandler(...) 注册一个写入处理器，这里用来自适应列宽，非常实用
            // .doWrite(dataList) 执行写入操作
            EasyExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(Boolean.TRUE) // 自动关闭流
                    .sheet(sheetName)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 注册一个自适应列宽的策略
                    .doWrite(dataList);

        } catch (IOException e) {
            log.error("导出Excel异常: {}", e.getMessage());
            // 如果发生异常，可以设置一个错误的响应状态
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            // 可以在响应体中写入错误信息
            try {
                response.getWriter().write("Failed to export Excel file.");
            } catch (IOException ex) {
                log.error("写入错误响应异常: {}", ex.getMessage());
            }
        }
    }
}