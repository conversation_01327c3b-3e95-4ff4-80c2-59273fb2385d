package com.zkdiman.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import com.zkdiman.common.config.TencentCosConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.ByteArrayInputStream;
import java.util.Date;

/**
 * 腾讯云COS工具类
 * 用于文件上传到腾讯云对象存储
 */
@Component
public class TencentCosUtils {
    
    private static final Logger logger = LogManager.getLogger(TencentCosUtils.class);
    
    @Autowired
    private TencentCosConfig cosConfig;
    
    private COSClient cosClient;
    
    /**
     * 初始化COS客户端
     */
    @PostConstruct
    public void initCosClient() {
        try {
            // 检查配置是否完整
            if (StrUtil.isBlank(cosConfig.getSecretId()) || StrUtil.isBlank(cosConfig.getSecretKey())) {
                logger.warn("腾讯云COS配置不完整，跳过初始化");
                return;
            }

            // 1 初始化用户身份信息（secretId, secretKey）
            COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());

            // 2 设置 bucket 的地域
            Region region = new Region(cosConfig.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);

            // 3 生成 cos 客户端
            this.cosClient = new COSClient(cred, clientConfig);

            logger.info("腾讯云COS客户端初始化成功，bucket: {}, region: {}",
                       cosConfig.getBucketName(), cosConfig.getRegion());
        } catch (Exception e) {
            logger.error("腾讯云COS客户端初始化失败: {}", e.getMessage(), e);
            // 在开发环境中，不要因为COS初始化失败而阻止应用启动
            logger.warn("COS客户端初始化失败，将在运行时检查可用性");
        }
    }
    
    /**
     * 销毁COS客户端
     */
    @PreDestroy
    public void destroyCosClient() {
        if (cosClient != null) {
            cosClient.shutdown();
            logger.info("腾讯云COS客户端已关闭");
        }
    }
    
    /**
     * 上传字节数组到COS
     * 
     * @param fileBytes 文件字节数组
     * @param fileName 文件名
     * @param contentType 文件类型
     * @return 文件的公网访问URL
     */
    public String uploadFile(byte[] fileBytes, String fileName, String contentType) {
        if (fileBytes == null || fileBytes.length == 0) {
            logger.warn("上传文件为空，跳过上传");
            return null;
        }

        if (StrUtil.isBlank(fileName)) {
            logger.warn("文件名为空，跳过上传");
            return null;
        }

        if (cosClient == null) {
            logger.error("腾讯云COS客户端未初始化，无法上传文件");
            return null;
        }

        try {
            // 生成带时间戳的文件路径
            String objectKey = generateObjectKey(fileName);
            
            // 创建上传请求
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(fileBytes.length);
            if (StrUtil.isNotBlank(contentType)) {
                metadata.setContentType(contentType);
            }
            
            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                cosConfig.getBucketName(), objectKey, inputStream, metadata);
            
            // 执行上传
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            
            // 生成访问URL
            String fileUrl = generateFileUrl(objectKey);
            
            logger.info("文件上传成功，文件名: {}, 大小: {} bytes, URL: {}", 
                       fileName, fileBytes.length, fileUrl);
            
            return fileUrl;
            
        } catch (CosServiceException e) {
            logger.error("腾讯云COS服务异常，上传文件失败: {}, 错误码: {}, 错误信息: {}", 
                        fileName, e.getErrorCode(), e.getErrorMessage(), e);
            return null;
        } catch (CosClientException e) {
            logger.error("腾讯云COS客户端异常，上传文件失败: {}, 错误信息: {}", 
                        fileName, e.getMessage(), e);
            return null;
        } catch (Exception e) {
            logger.error("上传文件到腾讯云COS失败: {}, 错误信息: {}", fileName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 生成对象键（文件在COS中的路径）
     * 
     * @param fileName 原始文件名
     * @return 对象键
     */
    private String generateObjectKey(String fileName) {
        // 格式：exports/2024/01/15/filename_timestamp.ext
        String dateFolder = DateUtil.format(new Date(), "yyyy/MM/dd");
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        // 提取文件扩展名
        String fileExtension = "";
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            fileExtension = fileName.substring(lastDotIndex);
            fileName = fileName.substring(0, lastDotIndex);
        }
        
        // 清理文件名，只保留安全字符
        String safeFileName = fileName.replaceAll("[^a-zA-Z0-9\u4E00-\u9FA5_.-]", "_");
        
        return String.format("%s/%s/%s_%s%s", 
                           cosConfig.getExportFolder(), 
                           dateFolder, 
                           safeFileName, 
                           timestamp, 
                           fileExtension);
    }
    
    /**
     * 生成文件的公网访问URL
     * 
     * @param objectKey 对象键
     * @return 公网访问URL
     */
    private String generateFileUrl(String objectKey) {
        // 如果配置了CDN域名，使用CDN域名
        if (StrUtil.isNotBlank(cosConfig.getCdnDomain())) {
            return String.format("https://%s/%s", cosConfig.getCdnDomain(), objectKey);
        }
        
        // 否则使用默认的COS域名
        return String.format("https://%s.cos.%s.myqcloud.com/%s", 
                           cosConfig.getBucketName(), 
                           cosConfig.getRegion(), 
                           objectKey);
    }
    
    /**
     * 检查COS客户端是否可用
     * 
     * @return true if available
     */
    public boolean isAvailable() {
        return cosClient != null;
    }
}
