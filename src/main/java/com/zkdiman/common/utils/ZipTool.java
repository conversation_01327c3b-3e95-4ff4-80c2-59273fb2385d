package com.zkdiman.common.utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件压缩工具类
 */
@Component
public class ZipTool {
    private static final Logger logger = LogManager.getLogger(ZipTool.class);

    /**
     * 将多个文件压缩成一个zip文件
     * @param sourceFiles 源文件列表
     * @param zipFile 目标zip文件
     * @return 是否压缩成功
     */
    public boolean zipFiles(List<File> sourceFiles, File zipFile) {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            
            for (File file : sourceFiles) {
                if (file.exists()) {
                    addFileToZip(file, file.getName(), zos);
                }
            }
            return true;
        } catch (IOException e) {
            logger.error("压缩文件失败", e);
            return false;
        }
    }
    
    /**
     * 将整个目录压缩成一个zip文件
     * @param sourceDirectory 源目录
     * @param zipFile 目标zip文件
     * @return 是否压缩成功
     */
    public boolean zipDirectory(File sourceDirectory, File zipFile) {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            
            zipDirectory(sourceDirectory, sourceDirectory.getName(), zos);
            return true;
        } catch (IOException e) {
            logger.error("压缩目录失败", e);
            return false;
        }
    }
    
    /**
     * 创建临时目录
     * @param prefix 目录前缀
     * @return 临时目录路径
     * @throws IOException 如果创建临时目录失败
     */
    public Path createTempDirectory(String prefix) throws IOException {
        return Files.createTempDirectory(prefix);
    }
    
    /**
     * 删除目录及其内容
     * @param directory 要删除的目录
     */
    public void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }
    
    // 递归添加文件夹到zip
    private void zipDirectory(File folder, String parentFolder, ZipOutputStream zos) throws IOException {
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    zipDirectory(file, parentFolder + "/" + file.getName(), zos);
                    continue;
                }
                
                try (FileInputStream fis = new FileInputStream(file)) {
                    String entryName = parentFolder + "/" + file.getName();
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zos.putNextEntry(zipEntry);
                    
                    byte[] bytes = new byte[1024];
                    int length;
                    while ((length = fis.read(bytes)) >= 0) {
                        zos.write(bytes, 0, length);
                    }
                }
            }
        }
    }
    
    // 添加单个文件到zip
    private void addFileToZip(File file, String entryName, ZipOutputStream zos) throws IOException {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File childFile : files) {
                    addFileToZip(childFile, entryName + "/" + childFile.getName(), zos);
                }
            }
        } else {
            try (FileInputStream fis = new FileInputStream(file)) {
                ZipEntry zipEntry = new ZipEntry(entryName);
                zos.putNextEntry(zipEntry);
                
                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zos.write(bytes, 0, length);
                }
            }
        }
    }
} 