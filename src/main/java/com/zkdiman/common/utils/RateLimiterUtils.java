package com.zkdiman.common.utils;

import com.google.common.util.concurrent.RateLimiter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 限流工具类
 * 基于Guava的RateLimiter实现，用于控制API请求频率
 */
@Component
public class RateLimiterUtils {
    private static final Logger logger = LogManager.getLogger(RateLimiterUtils.class);
    
    // 存储不同资源的RateLimiter
    private final Map<String, RateLimiter> limiters = new ConcurrentHashMap<>();
    
    // 默认每秒允许的请求数
    private static final double DEFAULT_PERMITS_PER_SECOND = 10.0;
    
    // 默认等待超时时间(毫秒)
    private static final long DEFAULT_TIMEOUT_MS = 100;
    
    /**
     * 获取指定资源的RateLimiter
     * @param resource 资源名称
     * @return RateLimiter实例
     */
    public RateLimiter getRateLimiter(String resource) {
        return limiters.computeIfAbsent(resource, k -> RateLimiter.create(DEFAULT_PERMITS_PER_SECOND));
    }
    
    /**
     * 获取指定资源的RateLimiter，如果不存在则创建并指定速率
     * @param resource 资源名称
     * @param permitsPerSecond 每秒允许的请求数
     * @return RateLimiter实例
     */
    public RateLimiter getRateLimiter(String resource, double permitsPerSecond) {
        return limiters.computeIfAbsent(resource, k -> RateLimiter.create(permitsPerSecond));
    }
    
    /**
     * 尝试获取许可
     * @param resource 资源名称
     * @return 是否成功获取许可
     */
    public boolean tryAcquire(String resource) {
        RateLimiter limiter = getRateLimiter(resource);
        return limiter.tryAcquire();
    }
    
    /**
     * 尝试在指定时间内获取许可
     * @param resource 资源名称
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取许可
     */
    public boolean tryAcquire(String resource, long timeout, TimeUnit unit) {
        RateLimiter limiter = getRateLimiter(resource);
        return limiter.tryAcquire(timeout, unit);
    }
    
    /**
     * 尝试获取指定数量的许可
     * @param resource 资源名称
     * @param permits 许可数量
     * @return 是否成功获取许可
     */
    public boolean tryAcquire(String resource, int permits) {
        RateLimiter limiter = getRateLimiter(resource);
        return limiter.tryAcquire(permits);
    }
    
    /**
     * 尝试获取指定资源的许可，使用默认超时时间
     * @param resource 资源名称
     * @return 是否成功获取许可
     */
    public boolean tryAcquireWithDefaultTimeout(String resource) {
        return tryAcquire(resource, DEFAULT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 更新限流器的速率
     * @param resource 资源名称
     * @param permitsPerSecond 每秒允许的请求数
     */
    public void updateRate(String resource, double permitsPerSecond) {
        RateLimiter limiter = getRateLimiter(resource);
        limiter.setRate(permitsPerSecond);
        logger.info("已更新资源[{}]的限流速率为: {}/秒", resource, permitsPerSecond);
    }
} 