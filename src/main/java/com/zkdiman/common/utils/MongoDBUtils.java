package com.zkdiman.common.utils;

import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * MongoDB操作工具类
 * 封装常用的MongoDB操作，简化代码
 */
@Component
public class MongoDBUtils {
    private static final Logger logger = LogManager.getLogger(MongoDBUtils.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 保存单个对象
     * @param object 要保存的对象
     * @param <T> 对象类型
     * @return 保存后的对象
     */
    public <T> T save(T object) {
        logger.debug("保存对象到MongoDB: {}", object.getClass().getSimpleName());
        return mongoTemplate.save(object);
    }

    /**
     * 批量保存对象
     * @param collection 对象集合
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 保存后的对象列表
     */
    public <T> Collection<T> saveAll(Collection<T> collection, Class<T> entityClass) {
        logger.debug("批量保存对象到MongoDB, 数量: {}", collection.size());
        return mongoTemplate.insert(collection, entityClass);
    }

    /**
     * 根据ID查询对象
     * @param id 对象ID
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 查询结果
     */
    public <T> T findById(String id, Class<T> entityClass) {
        logger.debug("根据ID查询对象: {}, ID: {}", entityClass.getSimpleName(), id);
        return mongoTemplate.findById(id, entityClass);
    }

    /**
     * 条件查询单个对象
     * @param criteria 查询条件
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 查询结果
     */
    public <T> T findOne(Criteria criteria, Class<T> entityClass) {
        Query query = new Query(criteria);
        logger.debug("条件查询单个对象: {}, 条件: {}", entityClass.getSimpleName(), criteria.getCriteriaObject());
        return mongoTemplate.findOne(query, entityClass);
    }

    /**
     * 条件查询对象列表
     * @param criteria 查询条件
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 查询结果列表
     */
    public <T> List<T> find(Criteria criteria, Class<T> entityClass) {
        Query query = new Query(criteria);
        logger.debug("条件查询对象列表: {}, 条件: {}", entityClass.getSimpleName(), criteria.getCriteriaObject());
        return mongoTemplate.find(query, entityClass);
    }

    /**
     * 查询对象列表
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 查询结果列表
     */
    public <T> List<T> findAll( Class<T> entityClass) {
        return mongoTemplate.findAll(entityClass);
    }


    /**
     * 分页查询
     * @param criteria 查询条件
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortField 排序字段
     * @param direction 排序方向
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 分页结果
     */
    public <T> Page<T> findPage(Criteria criteria, int page, int size, String sortField, Sort.Direction direction, Class<T> entityClass) {
        logger.debug("分页查询: {}, 页码: {}, 大小: {}", entityClass.getSimpleName(), page, size);
        
        // 创建分页对象
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(direction, sortField));
        
        // 创建查询对象
        Query query = new Query(criteria).with(pageRequest);
        
        // 查询当前页数据
        List<T> content = mongoTemplate.find(query, entityClass);
        
        // 查询总数
        Query countQuery = new Query(criteria);
        
        // 构造分页结果
        return PageableExecutionUtils.getPage(content, pageRequest, 
                () -> mongoTemplate.count(countQuery, entityClass));
    }

    /**
     * 根据条件更新单个对象
     * @param criteria 查询条件
     * @param updateMap 要更新的字段和值
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 是否成功
     */
    public <T> boolean updateOne(Criteria criteria, Map<String, Object> updateMap, Class<T> entityClass) {
        Query query = new Query(criteria);
        Update update = new Update();
        
        updateMap.forEach(update::set);
        
        logger.debug("更新单个对象: {}, 条件: {}", entityClass.getSimpleName(), criteria.getCriteriaObject());
        return mongoTemplate.updateFirst(query, update, entityClass).wasAcknowledged();
    }


    /**
     * 根据条件更新多个对象
     * @param criteria 查询条件
     * @param updateMap 要更新的字段和值
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 是否成功
     */
    public <T> boolean updateMany(Criteria criteria, Map<String, Object> updateMap, Class<T> entityClass) {
        Query query = new Query(criteria);
        Update update = new Update();
        
        updateMap.forEach(update::set);
        
        logger.debug("更新多个对象: {}, 条件: {}", entityClass.getSimpleName(), criteria.getCriteriaObject());
        return mongoTemplate.updateMulti(query, update, entityClass).wasAcknowledged();
    }

    /**
     * 数组添加元素
     * @param criteria 查询条件
     * @param arrayField 数组字段名
     * @param value 要添加的值
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 是否成功
     */
    public <T> boolean addToArray(Criteria criteria, String arrayField, Object value, Class<T> entityClass) {
        Query query = new Query(criteria);
        Update update = new Update().push(arrayField, value);
        
        logger.debug("数组添加元素: {}.{}, 条件: {}", entityClass.getSimpleName(), arrayField, criteria.getCriteriaObject());
        return mongoTemplate.updateFirst(query, update, entityClass).wasAcknowledged();
    }

    /**
     * 数组删除元素
     * @param criteria 查询条件
     * @param arrayField 数组字段名
     * @param value 要删除的值
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 是否成功
     */
    public <T> boolean removeFromArray(Criteria criteria, String arrayField, Object value, Class<T> entityClass) {
        Query query = new Query(criteria);
        Update update = new Update().pull(arrayField, value);
        
        logger.debug("数组删除元素: {}.{}, 条件: {}", entityClass.getSimpleName(), arrayField, criteria.getCriteriaObject());
        return mongoTemplate.updateFirst(query, update, entityClass).wasAcknowledged();
    }

    /**
     * 删除单个对象
     * @param criteria 查询条件
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 是否成功
     */
    public <T> boolean deleteOne(Criteria criteria, Class<T> entityClass) {
        Query query = new Query(criteria);
        
        logger.debug("删除单个对象: {}, 条件: {}", entityClass.getSimpleName(), criteria.getCriteriaObject());
        return mongoTemplate.remove(query, entityClass).wasAcknowledged();
    }

    /**
     * 计数
     * @param criteria 查询条件
     * @param entityClass 对象类型
     * @param <T> 对象类型
     * @return 计数结果
     */
    public <T> long count(Criteria criteria, Class<T> entityClass) {
        Query query = new Query(criteria);
        
        logger.debug("计数查询: {}, 条件: {}", entityClass.getSimpleName(), criteria.getCriteriaObject());
        return mongoTemplate.count(query, entityClass);
    }


    // 使用MongoDB查询条件根据examId查询
    //List<CompetitionQuestionLog> logs = mongoDBUtils.findByField("examId", examId, CompetitionQuestionLog.class);

    /**
     * 根据字段查询
     * @param field
     * @param value
     * @param entityClass
     * @return
     * @param <T>
     */
    public <T> List<T> findByField(String field, Object value, Class<T> entityClass) {
        Query query = new Query(Criteria.where(field).is(value));
        return mongoTemplate.find(query, entityClass);
    }


}