package com.zkdiman.common.utils;

import com.zkdiman.common.exception.UploadException;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 文件校验工具类
 * 用于验证上传文件的类型和大小
 */
public class FileValidationUtils {

    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"
    );
    
    // 默认最大文件大小为5MB
    private static final long DEFAULT_MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 验证图片文件类型
     * @param file 上传的文件
     * @throws UploadException 如果文件类型不是图片则抛出异常
     */
    public static void validateImageType(MultipartFile file) {
        if (file == null) {
            throw new UploadException("文件不能为空");
        }

        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_IMAGE_TYPES.contains(contentType)) {
            throw new UploadException("请上传有效的图片文件格式(JPEG, PNG, GIF, BMP, WEBP)");
        }
    }

    /**
     * 验证文件大小
     * @param file 上传的文件
     * @param maxSize 最大允许大小(字节)
     * @throws UploadException 如果文件大小超过限制则抛出异常
     */
    public static void validateFileSize(MultipartFile file, long maxSize) {
        if (file == null) {
            throw new UploadException("文件不能为空");
        }

        if (file.getSize() > maxSize) {
            throw new UploadException("文件大小超过限制，最大允许" + (maxSize / 1024 / 1024) + "MB");
        }
    }

    /**
     * 验证图片文件(类型和大小)
     * @param file 上传的文件
     * @throws UploadException 如果文件验证失败则抛出异常
     */
    public static void validateImage(MultipartFile file) {
        validateImageType(file);
        validateFileSize(file, DEFAULT_MAX_FILE_SIZE);
    }

    /**
     * 验证图片文件(类型和自定义大小)
     * @param file 上传的文件
     * @param maxSize 最大允许大小(字节)
     * @throws UploadException 如果文件验证失败则抛出异常
     */
    public static void validateImage(MultipartFile file, long maxSize) {
        validateImageType(file);
        validateFileSize(file, maxSize);
    }
} 