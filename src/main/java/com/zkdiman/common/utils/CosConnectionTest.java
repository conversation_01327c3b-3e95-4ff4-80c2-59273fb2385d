package com.zkdiman.common.utils;

import cn.hutool.core.util.StrUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.Bucket;
import com.qcloud.cos.region.Region;
import com.zkdiman.common.config.TencentCosConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 腾讯云COS连接测试工具
 * 用于验证COS配置是否正确
 */
@Component
public class CosConnectionTest {
    
    private static final Logger logger = LogManager.getLogger(CosConnectionTest.class);
    
    @Autowired
    private TencentCosConfig cosConfig;
    
    /**
     * 测试COS连接并列出所有存储桶
     */
    public void testConnection() {
        logger.info("开始测试腾讯云COS连接...");
        
        if (StrUtil.isBlank(cosConfig.getSecretId()) || StrUtil.isBlank(cosConfig.getSecretKey())) {
            logger.error("COS配置不完整：SecretId或SecretKey为空");
            return;
        }
        
        COSClient cosClient = null;
        try {
            // 初始化COS客户端
            COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
            Region region = new Region(cosConfig.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            cosClient = new COSClient(cred, clientConfig);
            
            logger.info("COS客户端初始化成功，配置信息：");
            logger.info("- SecretId: {}***", cosConfig.getSecretId().substring(0, 8));
            logger.info("- Region: {}", cosConfig.getRegion());
            logger.info("- BucketName: {}", cosConfig.getBucketName());
            
            // 列出所有存储桶
            logger.info("正在获取存储桶列表...");
            List<Bucket> buckets = cosClient.listBuckets();
            
            logger.info("找到 {} 个存储桶：", buckets.size());
            for (Bucket bucket : buckets) {
                logger.info("- 存储桶名称: {}, 地域: {}, 创建时间: {}", 
                           bucket.getName(), 
                           bucket.getLocation(), 
                           bucket.getCreationDate());
                
                // 检查是否是目标存储桶
                if (bucket.getName().equals(cosConfig.getBucketName())) {
                    logger.info("✓ 找到目标存储桶: {}", bucket.getName());
                    
                    // 检查地域是否匹配
                    if (bucket.getLocation().equals(cosConfig.getRegion())) {
                        logger.info("✓ 存储桶地域匹配: {}", bucket.getLocation());
                    } else {
                        logger.warn("✗ 存储桶地域不匹配！配置地域: {}, 实际地域: {}", 
                                   cosConfig.getRegion(), bucket.getLocation());
                        logger.warn("请将配置文件中的region修改为: {}", bucket.getLocation());
                    }
                }
            }
            
            // 检查目标存储桶是否存在
            boolean targetBucketFound = buckets.stream()
                    .anyMatch(bucket -> bucket.getName().equals(cosConfig.getBucketName()));
            
            if (!targetBucketFound) {
                logger.error("✗ 未找到目标存储桶: {}", cosConfig.getBucketName());
                logger.error("请检查存储桶名称是否正确，或者该存储桶是否在当前账号下");
            }
            
        } catch (CosServiceException e) {
            logger.error("COS服务异常：");
            logger.error("- 错误码: {}", e.getErrorCode());
            logger.error("- 错误信息: {}", e.getErrorMessage());
            logger.error("- HTTP状态码: {}", e.getStatusCode());
            logger.error("- 请求ID: {}", e.getRequestId());
        } catch (CosClientException e) {
            logger.error("COS客户端异常: {}", e.getMessage());
        } catch (Exception e) {
            logger.error("连接测试失败: {}", e.getMessage(), e);
        } finally {
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
        
        logger.info("COS连接测试完成");
    }
    
    /**
     * 测试指定地域的连接
     */
    public void testConnectionWithRegion(String region) {
        logger.info("测试地域 {} 的COS连接...", region);
        
        COSClient cosClient = null;
        try {
            COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
            Region testRegion = new Region(region);
            ClientConfig clientConfig = new ClientConfig(testRegion);
            cosClient = new COSClient(cred, clientConfig);
            
            List<Bucket> buckets = cosClient.listBuckets();
            logger.info("地域 {} 中找到 {} 个存储桶", region, buckets.size());
            
            for (Bucket bucket : buckets) {
                if (bucket.getName().equals(cosConfig.getBucketName())) {
                    logger.info("✓ 在地域 {} 中找到目标存储桶: {}", region, bucket.getName());
                    return;
                }
            }
            
            logger.info("在地域 {} 中未找到目标存储桶", region);
            
        } catch (Exception e) {
            logger.error("测试地域 {} 失败: {}", region, e.getMessage());
        } finally {
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
    }
}
