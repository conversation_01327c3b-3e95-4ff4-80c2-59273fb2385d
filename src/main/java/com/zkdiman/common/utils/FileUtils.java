package com.zkdiman.common.utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件操作工具类
 */
@Component
public class FileUtils {
    private static final Logger logger = LogManager.getLogger(FileUtils.class);
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    
    /**
     * 生成唯一文件名
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    public String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        return timestamp + "_" + uuid + extension;
    }
    
    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 文件扩展名（包含点号）
     */
    public String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }
    
    /**
     * 验证文件类型是否合法
     * @param file 文件
     * @param allowedTypes 允许的MIME类型数组
     * @return 是否合法
     */
    public boolean isValidFileType(MultipartFile file, String[] allowedTypes) {
        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }
        
        for (String type : allowedTypes) {
            if (contentType.equals(type)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 将MultipartFile转换为File
     * @param file MultipartFile对象
     * @param targetDir 目标目录
     * @return 转换后的File对象，转换失败返回null
     */
    public File convertMultipartFileToFile(MultipartFile file, String targetDir) {
        if (file == null || file.isEmpty()) {
            return null;
        }
        
        // 确保目标目录存在
        File dir = new File(targetDir);
        if (!dir.exists() && !dir.mkdirs()) {
            logger.error("无法创建目录: {}", targetDir);
            return null;
        }
        
        // 创建新文件
        String uniqueFileName = generateUniqueFileName(file.getOriginalFilename());
        File convertedFile = new File(dir, uniqueFileName);
        
        try (FileOutputStream fos = new FileOutputStream(convertedFile)) {
            fos.write(file.getBytes());
            return convertedFile;
        } catch (IOException e) {
            logger.error("MultipartFile转换为File失败", e);
            return null;
        }
    }
    
    /**
     * 创建临时文件
     * @param prefix 前缀
     * @param suffix 后缀
     * @param content 文件内容
     * @return 临时文件
     * @throws IOException 如果创建过程中出错
     */
    public File createTempFile(String prefix, String suffix, byte[] content) throws IOException {
        File tempFile = File.createTempFile(prefix, suffix);
        tempFile.deleteOnExit();
        
        if (content != null) {
            try (FileOutputStream out = new FileOutputStream(tempFile)) {
                out.write(content);
            }
        }
        
        return tempFile;
    }
    
    /**
     * 复制输入流到文件
     * @param input 输入流
     * @param targetFile 目标文件
     * @return 是否复制成功
     */
    public boolean copyInputStreamToFile(InputStream input, File targetFile) {
        try (FileOutputStream out = new FileOutputStream(targetFile)) {
            byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return true;
        } catch (IOException e) {
            logger.error("复制输入流到文件失败", e);
            return false;
        }
    }
    
    /**
     * 安全删除文件
     * @param file 要删除的文件
     * @return 是否删除成功
     */
    public boolean safeDelete(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        try {
            if (file.isDirectory()) {
                Files.walk(file.toPath())
                        .sorted((p1, p2) -> -p1.compareTo(p2))
                        .map(Path::toFile)
                        .forEach(File::delete);
            } else {
                file.delete();
            }
            return true;
        } catch (IOException e) {
            logger.error("安全删除文件失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }
} 