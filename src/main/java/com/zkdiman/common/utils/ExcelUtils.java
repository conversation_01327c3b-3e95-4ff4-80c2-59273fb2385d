package com.zkdiman.common.utils;

import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel操作工具类
 * 提供获取单元格字符串值的方法
 */
@Component
public class ExcelUtils {
    private  static final Logger logger = LogManager.getLogger(ExcelUtils.class);

    /**
     * 获取单元格的字符串值
     *
     * @param cell    单元格对象
     * @param workbook 工作簿对象
     * @return 单元格的字符串值
     */
    public static String getCellStringValue(Cell cell, Workbook workbook) {
        if (cell == null) {
            return null;
        }
        DataFormatter formatter = new DataFormatter();
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

        String cellValue = null;
        switch (cell.getCellTypeEnum()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                cellValue = formatter.formatCellValue(cell);
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                try {
                    cellValue = formatter.formatCellValue(cell, evaluator);
                } catch (Exception e) {
                    logger.warn("无法评估Excel行中单元格公式，单元格地址: {}, 错误: {}", cell.getAddress(), e.getMessage());
                    cellValue = null;
                }
                break;
            case BLANK:
            default:
                cellValue = null;
                break;
        }
        return cellValue != null ? cellValue.trim() : null;
    }


    public static String getCellValueAsString(Cell cell, DataFormatter dataFormatter, Workbook workbook) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellTypeEnum() == CellType.FORMULA) {
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            try {
                return dataFormatter.formatCellValue(cell, evaluator).trim();
            } catch (Exception e) {
                logger.warn("Could not evaluate formula in cell {}:{}: {}", cell.getRowIndex(), cell.getColumnIndex(), e.getMessage());
                return "#FORMULA_ERROR#";
            }
        }
        return dataFormatter.formatCellValue(cell).trim();
    }


    public static List<StudentExcelDataDTO> parseExcelFile(MultipartFile file, List<BatchImportResultVO.ImportError> errors) {
        List<StudentExcelDataDTO> studentDataList = new ArrayList<>();
        if (file.isEmpty()) {
            logger.warn("Uploaded Excel file is empty.");
            errors.add(BatchImportResultVO.ImportError.builder()
                    .rowNumber(0)
                    .identifier("N/A")
                    .reason("上传的Excel文件不能为空")
                    .build());
            return studentDataList;
        }

        logger.info("Starting Excel parsing for file: {}", file.getOriginalFilename());
        DataFormatter dataFormatter = new DataFormatter();

        try (InputStream is = file.getInputStream(); Workbook workbook = WorkbookFactory.create(is)) {
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                errors.add(BatchImportResultVO.ImportError.builder().rowNumber(0).identifier("N/A").reason("Excel文件中没有工作表。").build());
                return studentDataList;
            }

            int headerRowIndex = 0;
            Row headerRow = sheet.getRow(headerRowIndex);
            if (headerRow == null) {
                errors.add(BatchImportResultVO.ImportError.builder().rowNumber(0).identifier("N/A").reason("Excel文件缺少表头行。").build());
                return studentDataList;
            }

            for (int rowIndex = headerRowIndex + 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row currentRow = sheet.getRow(rowIndex);
                if (currentRow == null) {
                    logger.debug("Skipping empty row at index: {}", rowIndex);
                    continue;
                }
                boolean isRowBlank = true;
                for (int cn = 0; cn < headerRow.getLastCellNum(); cn++) {
                    Cell cell = currentRow.getCell(cn, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                    if (cell != null && cell.getCellTypeEnum() != CellType.BLANK) {
                        String cellValue = dataFormatter.formatCellValue(cell);
                        if(StringUtils.hasText(cellValue)){
                            isRowBlank = false;
                            break;
                        }
                    }
                }
                if (isRowBlank) {
                    logger.debug("Skipping visually blank row at index: {}", rowIndex);
                    continue;
                }

                StudentExcelDataDTO studentData = new StudentExcelDataDTO();
                studentData.setExcelRowNumber(rowIndex + 1);

                try {
                    // 按照StudentExcelDataDTO中属性的顺序进行赋值
                    // 根据StudentServiceImpl.java中batchImportStudents方法的列映射

                    // excelRowNumber已经在上面设置
                    
                    // 0: 年级 (grade)
                    studentData.setGrade(getCellValueAsString(currentRow.getCell(0), dataFormatter, workbook));
                    
                    // 1: 班级 (className)
                    studentData.setClassName(getCellValueAsString(currentRow.getCell(1), dataFormatter, workbook));
                    
                    // 2: 姓名 (name)
                    studentData.setName(getCellValueAsString(currentRow.getCell(2), dataFormatter, workbook));
                    
                    // 3: 性别 (gender)
                    String genderStr = getCellValueAsString(currentRow.getCell(3), dataFormatter, workbook);
                    if (StringUtils.hasText(genderStr)) {
                        studentData.setGender(Integer.valueOf(genderStr));
                    }
                    
                    // 4: 身份证号 (idCard)
                    studentData.setIdCard(getCellValueAsString(currentRow.getCell(4), dataFormatter, workbook));
                    
                    // 5: 准考证号/学籍号 (examCard)
                    studentData.setExamCard(getCellValueAsString(currentRow.getCell(5), dataFormatter, workbook));
                    
                    // 6: 民族 (nation)
                    studentData.setNation(getCellValueAsString(currentRow.getCell(6), dataFormatter, workbook));
                    
                    // 7: 就读方式 (studyMode)
                    String studyModeStr = getCellValueAsString(currentRow.getCell(7), dataFormatter, workbook);
                    if (StringUtils.hasText(studyModeStr)) {
                        studentData.setStudyMode(Integer.valueOf(studyModeStr));
                    }
                    
                    // 8: 户口本性质 (householdType)
                    String householdTypeStr = getCellValueAsString(currentRow.getCell(8), dataFormatter, workbook);
                    if (StringUtils.hasText(householdTypeStr)) {
                        studentData.setHouseholdType(Integer.valueOf(householdTypeStr));
                    }
                    
                    // 9: 学校 (school)
                    studentData.setSchool(getCellValueAsString(currentRow.getCell(9), dataFormatter, workbook));
                    
                    // 10: 学校区名 (zoneName)
                    studentData.setZoneName(getCellValueAsString(currentRow.getCell(10), dataFormatter, workbook));
                    
                    // 11: 学校代码 (schoolCode)
                    studentData.setSchoolCode(getCellValueAsString(currentRow.getCell(11), dataFormatter, workbook));
                    
                    // 12: 本科科学教师数 (teacherNumber)
                    studentData.setTeacherNumber(getCellValueAsString(currentRow.getCell(12), dataFormatter, workbook));
                    
                    // 13: 学校负责人员的联系电话 (contactNumber)
                    studentData.setContactNumber(getCellValueAsString(currentRow.getCell(13), dataFormatter, workbook));
                    
                    // 14: 考场 (examRoom)
                    studentData.setExamRoom(getCellValueAsString(currentRow.getCell(14), dataFormatter, workbook));
                    
                    // 15: 考场号 (examRoomNumber)
                    studentData.setExamRoomNumber(getCellValueAsString(currentRow.getCell(15), dataFormatter, workbook));
                    
                    // 16: 座位号 (seatNumber)
                    studentData.setSeatNumber(getCellValueAsString(currentRow.getCell(16), dataFormatter, workbook));
                    
                    // 17: 考试地点 (examLocation)
                    studentData.setExamLocation(getCellValueAsString(currentRow.getCell(17), dataFormatter, workbook));
                    
                    // 成绩部分 - 18-22列是五个科目的成绩
                    try {
                        Integer[] scores = new Integer[5];
                        
                        // 18: 语文成绩
                        String chineseScoreStr = getCellValueAsString(currentRow.getCell(18), dataFormatter, workbook);
                        if (StringUtils.hasText(chineseScoreStr)) {
                            scores[0] = Integer.valueOf(chineseScoreStr);
                        }
                        
                        // 19: 数学成绩
                        String mathScoreStr = getCellValueAsString(currentRow.getCell(19), dataFormatter, workbook);
                        if (StringUtils.hasText(mathScoreStr)) {
                            scores[1] = Integer.valueOf(mathScoreStr);
                        }
                        
                        // 20: 英语成绩
                        String englishScoreStr = getCellValueAsString(currentRow.getCell(20), dataFormatter, workbook);
                        if (StringUtils.hasText(englishScoreStr)) {
                            scores[2] = Integer.valueOf(englishScoreStr);
                        }
                        
                        // 21: 物理成绩
                        String physicsScoreStr = getCellValueAsString(currentRow.getCell(21), dataFormatter, workbook);
                        if (StringUtils.hasText(physicsScoreStr)) {
                            scores[3] = Integer.valueOf(physicsScoreStr);
                        }
                        
                        // 22: 政治成绩
                        String politicsScoreStr = getCellValueAsString(currentRow.getCell(22), dataFormatter, workbook);
                        if (StringUtils.hasText(politicsScoreStr)) {
                            scores[4] = Integer.valueOf(politicsScoreStr);
                        }
                        
                        studentData.setScores(scores);
                    } catch (NumberFormatException e) {
                        logger.warn("成绩格式转换失败，行号: {}, 错误: {}", rowIndex + 1, e.getMessage());
                    }

                    studentDataList.add(studentData);

                } catch (Exception cellParsingException) {
                    logger.warn("Error parsing cells in Excel row {}: {}", rowIndex + 1, cellParsingException.getMessage());
                    errors.add(BatchImportResultVO.ImportError.builder()
                            .rowNumber(rowIndex + 1)
                            .identifier(getCellValueAsString(currentRow.getCell(5), dataFormatter, workbook)) // 使用第6列（索引5）的准考证号的值作为标识符
                            .reason("行内数据单元格解析错误: " + cellParsingException.getMessage())
                            .build());
                }
            }
        } catch (IOException e) {
            logger.error("IO Error parsing Excel file: {}", file.getOriginalFilename(), e);
            errors.add(BatchImportResultVO.ImportError.builder().rowNumber(0).identifier("N/A").reason("Excel文件读取IO错误: " + e.getMessage()).build());
        } catch (org.apache.poi.EmptyFileException e) {
            logger.error("Uploaded file is empty or not a valid Excel file (EmptyFileException): {}", file.getOriginalFilename(), e);
            errors.add(BatchImportResultVO.ImportError.builder().rowNumber(0).identifier("N/A").reason("上传的文件为空或不是有效的Excel文件。").build());
        } catch (Exception e) {
            logger.error("Generic error parsing Excel file: {}", file.getOriginalFilename(), e);
            errors.add(BatchImportResultVO.ImportError.builder().rowNumber(0).identifier("N/A").reason("Excel文件解析失败: " + e.getMessage()).build());
        }
        return studentDataList;
    }
}
