package com.zkdiman.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.net.URL;
import java.util.concurrent.TimeUnit;

/**
 * URL转图片转换器
 * 用于EasyExcel将URL字段转换为Excel中的图片
 */
public class UrlToImageConverter implements Converter<URL> {

    private static final Logger logger = LogManager.getLogger(UrlToImageConverter.class);

    // 使用OkHttp客户端，配置超时时间
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    // 图片大小限制：5MB
    private static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024;
    
    @Override
    public Class<URL> supportJavaTypeKey() {
        return URL.class;
    }
    
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.IMAGE;
    }
    
    @Override
    public WriteCellData<URL> convertToExcelData(URL value, ExcelContentProperty contentProperty,
                                                 GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            return new WriteCellData<>();
        }
        
        try {
            // 下载图片数据
            byte[] imageBytes = downloadImageFromUrl(value);
            if (imageBytes != null && imageBytes.length > 0) {
                WriteCellData<URL> cellData = new WriteCellData<>();
                cellData.setType(CellDataTypeEnum.IMAGE);
                cellData.setImageValue(imageBytes);
                return cellData;
            } else {
                logger.warn("无法下载图片，URL: {}", value);
                // 返回空的单元格数据
                return new WriteCellData<>();
            }
        } catch (Exception e) {
            logger.error("转换URL到图片失败，URL: {}, 错误: {}", value, e.getMessage());
            // 发生异常时返回空的单元格数据
            return new WriteCellData<>();
        }
    }
    
    /**
     * 从URL下载图片数据
     *
     * @param imageUrl 图片URL
     * @return 图片字节数组
     */
    private byte[] downloadImageFromUrl(URL imageUrl) {
        try {
            Request request = new Request.Builder()
                    .url(imageUrl)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .build();

            try (Response response = HTTP_CLIENT.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    // 检查Content-Length头，如果存在且超过限制，直接拒绝
                    String contentLength = response.header("Content-Length");
                    if (contentLength != null) {
                        long size = Long.parseLong(contentLength);
                        if (size > MAX_IMAGE_SIZE) {
                            logger.warn("图片文件过大，URL: {}, 大小: {} bytes", imageUrl, size);
                            return null;
                        }
                    }

                    byte[] bytes = response.body().bytes();

                    // 检查实际下载的文件大小
                    if (bytes.length > MAX_IMAGE_SIZE) {
                        logger.warn("图片文件过大，URL: {}, 实际大小: {} bytes", imageUrl, bytes.length);
                        return null;
                    }

                    logger.debug("成功下载图片，URL: {}, 大小: {} bytes", imageUrl, bytes.length);
                    return bytes;
                } else {
                    logger.warn("下载图片失败，HTTP状态码: {} for URL: {}", response.code(), imageUrl);
                    return null;
                }
            }
        } catch (Exception e) {
            logger.error("下载图片异常，URL: {}, 错误: {}", imageUrl, e.getMessage());
            return null;
        }
    }
}
