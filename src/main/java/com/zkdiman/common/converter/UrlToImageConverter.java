package com.zkdiman.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.URL;

/**
 * URL转文本转换器
 * 用于EasyExcel将URL字段转换为Excel中的文本
 * 注意：由于EasyExcel图片API的复杂性，暂时转换为文本显示
 */
public class UrlToImageConverter implements Converter<URL> {

    private static final Logger logger = LogManager.getLogger(UrlToImageConverter.class);
    
    @Override
    public Class<URL> supportJavaTypeKey() {
        return URL.class;
    }
    
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(URL value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        WriteCellData<?> cellData = new WriteCellData<>();
        cellData.setType(CellDataTypeEnum.STRING);

        if (value == null) {
            logger.debug("URL为空，返回空单元格");
            cellData.setStringValue("");
            return cellData;
        }

        // 暂时返回URL文本，后续可以扩展为图片下载
        logger.info("转换URL为文本，URL: {}", value);
        cellData.setStringValue(value.toString());
        return cellData;
    }

}
