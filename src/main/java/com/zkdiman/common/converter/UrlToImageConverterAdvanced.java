package com.zkdiman.common.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.IoUtils;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.InputStream;
import java.net.URL;
import java.util.concurrent.TimeUnit;

/**
 * 高级URL转图片转换器
 * 使用EasyExcel 3.3.4的正确API来处理图片
 */
public class UrlToImageConverterAdvanced implements Converter<URL> {

    private static final Logger logger = LogManager.getLogger(UrlToImageConverterAdvanced.class);

    // HTTP客户端配置
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();

    // 图片大小限制：10MB
    private static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024;

    @Override
    public Class<URL> supportJavaTypeKey() {
        return URL.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(URL value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value == null) {
            logger.debug("URL为空，返回空单元格");
            WriteCellData<?> cellData = new WriteCellData<>();
            cellData.setType(CellDataTypeEnum.STRING);
            cellData.setStringValue("");
            return cellData;
        }

        try {
            logger.info("开始处理图片URL: {}", value);
            
            // 尝试下载图片
            byte[] imageBytes = downloadImageFromUrl(value);
            if (imageBytes != null && imageBytes.length > 0) {
                // 创建图片单元格数据
                WriteCellData<?> cellData = new WriteCellData<>();
                
                // 尝试使用EasyExcel的图片处理方式
                try {
                    // 对于EasyExcel 3.3.4，我们需要使用InputStream
                    java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(imageBytes);
                    
                    // 设置为图片类型（如果支持）
                    cellData.setType(CellDataTypeEnum.STRING);
                    
                    // 尝试设置图片数据
                    // 注意：这里可能需要根据实际的EasyExcel版本调整
                    cellData.setData(inputStream);
                    
                    logger.info("成功设置图片数据，URL: {}, 大小: {} bytes", value, imageBytes.length);
                    return cellData;
                    
                } catch (Exception imgEx) {
                    logger.warn("图片数据设置失败，降级为URL文本，URL: {}, 错误: {}", value, imgEx.getMessage());
                    // 降级为URL文本
                    cellData.setType(CellDataTypeEnum.STRING);
                    cellData.setStringValue(value.toString());
                    return cellData;
                }
            } else {
                logger.warn("无法下载图片，显示URL文本，URL: {}", value);
                WriteCellData<?> cellData = new WriteCellData<>();
                cellData.setType(CellDataTypeEnum.STRING);
                cellData.setStringValue(value.toString());
                return cellData;
            }
        } catch (Exception e) {
            logger.error("处理图片URL失败，URL: {}, 错误: {}", value, e.getMessage(), e);
            // 异常时返回URL文本
            WriteCellData<?> cellData = new WriteCellData<>();
            cellData.setType(CellDataTypeEnum.STRING);
            cellData.setStringValue(value.toString());
            return cellData;
        }
    }

    /**
     * 从URL下载图片数据
     */
    private byte[] downloadImageFromUrl(URL imageUrl) {
        Response response = null;
        try {
            logger.debug("开始下载图片: {}", imageUrl);

            Request request = new Request.Builder()
                    .url(imageUrl)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .addHeader("Accept", "image/*")
                    .build();

            response = HTTP_CLIENT.newCall(request).execute();

            if (!response.isSuccessful()) {
                logger.warn("下载图片失败，HTTP状态码: {} for URL: {}", response.code(), imageUrl);
                return null;
            }

            if (response.body() == null) {
                logger.warn("响应体为空，URL: {}", imageUrl);
                return null;
            }

            // 检查Content-Length头
            String contentLength = response.header("Content-Length");
            if (contentLength != null) {
                try {
                    long size = Long.parseLong(contentLength);
                    if (size > MAX_IMAGE_SIZE) {
                        logger.warn("图片文件过大，URL: {}, 大小: {} bytes", imageUrl, size);
                        return null;
                    }
                } catch (NumberFormatException e) {
                    logger.debug("无法解析Content-Length头: {}, URL: {}", contentLength, imageUrl);
                }
            }

            // 使用EasyExcel的IoUtils来读取字节数组
            InputStream inputStream = response.body().byteStream();
            byte[] bytes = IoUtils.toByteArray(inputStream);

            // 检查实际下载的文件大小
            if (bytes.length > MAX_IMAGE_SIZE) {
                logger.warn("图片文件过大，URL: {}, 实际大小: {} bytes", imageUrl, bytes.length);
                return null;
            }

            logger.debug("成功下载图片，URL: {}, 大小: {} bytes", imageUrl, bytes.length);
            return bytes;

        } catch (Exception e) {
            logger.error("下载图片异常，URL: {}, 错误: {}", imageUrl, e.getMessage(), e);
            return null;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }
}
