package com.zkdiman.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zkdiman.common.annotation.LogRecord;
import com.zkdiman.pojo.entity.LogInfo;
import com.zkdiman.server.config.LoggingConfig;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import java.util.Arrays;


/**
 * 日志记录切面类
 */
@Slf4j
@Aspect
@Component
public class LoggingAspect {
    
    @Autowired
    private LoggingConfig loggingConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("loggingObjectMapper")
    private ObjectMapper loggingObjectMapper;
    
    /**
     * 定义切点：所有标记了@LogRecord注解的方法
     */
    @Pointcut("@annotation(com.zkdiman.common.annotation.LogRecord)")
    public void logRecordPointcut() {}
    
    /**
     * 定义切点：基于配置文件的包路径
     */
    @Pointcut("execution(* com.zkdiman.server.service..*.*(..)) || execution(* com.zkdiman.server.controller..*.*(..))")
    public void configBasedPointcut() {}
    
    /**
     * 环绕通知：处理注解方式的日志记录
     */
    @Around("logRecordPointcut()")
    public Object aroundLogRecord(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!loggingConfig.isEnabled()) {
            return joinPoint.proceed();
        }
        
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        LogRecord logRecord = method.getAnnotation(LogRecord.class);
        
        return executeWithLogging(joinPoint, logRecord.value(), 
            logRecord.logArgs(), logRecord.logResult(), logRecord.logExecutionTime());
    }
    
    /**
     * 环绕通知：处理配置文件方式的日志记录
     */
    @Around("configBasedPointcut() && !logRecordPointcut()")
    public Object aroundConfigBased(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!loggingConfig.isEnabled() || !shouldLogByConfig(joinPoint)) {
            return joinPoint.proceed();
        }
        
        return executeWithLogging(joinPoint, "", 
            loggingConfig.isLogArgs(), loggingConfig.isLogResult(), loggingConfig.isLogExecutionTime());
    }
    
    /**
     * 执行方法并记录日志的核心逻辑
     */
    private Object executeWithLogging(ProceedingJoinPoint joinPoint, String description,
                                    boolean logArgsConfig, boolean logResultConfig, boolean logExecutionTimeConfig) throws Throwable {

        LogInfo logInfo = new LogInfo();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();

        // 填充基本信息
        logInfo.setPackageName(signature.getDeclaringType().getPackage().getName());
        logInfo.setClassName(signature.getDeclaringType().getSimpleName());
        logInfo.setMethodName(signature.getName());
        logInfo.setDescription(description);
        logInfo.setTimestamp(LocalDateTime.now());

        // 记录请求参数
        if (logArgsConfig) {
            Object[] args = joinPoint.getArgs();
            Object[] argsToLog = new Object[args.length];
            for (int i = 0; i < args.length; i++) {
                argsToLog[i] = sanitizeArgument(args[i]);
            }
            logInfo.setArgs(argsToLog);
        }

        long startTime = System.currentTimeMillis();
        Object result = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            logInfo.setSuccess(true);

            // 记录返回结果
            if (logResultConfig) {
                logInfo.setResult(result);
            }

        } catch (Throwable throwable) {
            logInfo.setSuccess(false);
            logInfo.setErrorMessage(throwable.getMessage());
            throw throwable; // 继续向上抛出异常
        } finally {
            // 记录执行时间
            if (logExecutionTimeConfig) {
                logInfo.setExecutionTime(System.currentTimeMillis() - startTime);
            }

            // 输出日志
            printLog(logInfo);
        }

        return result;
    }
    
    /**
     * 根据配置判断是否需要记录日志
     */
    private boolean shouldLogByConfig(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String packageName = signature.getDeclaringType().getPackage().getName();
        String className = signature.getDeclaringType().getSimpleName();
        String methodName = signature.getName();
        
        log.debug("检查日志配置: {}.{}.{}", packageName, className, methodName);
        
        // 检查排除列表
        if (isInExcludeList(packageName, className, methodName)) {
            log.debug("方法在排除列表中，跳过日志记录: {}.{}.{}", packageName, className, methodName);
            return false;
        }
        
        // 检查包含列表
        boolean shouldLog = isInIncludeList(packageName, className, methodName);
        if (shouldLog) {
            log.debug("方法在包含列表中，将记录日志: {}.{}.{}", packageName, className, methodName);
        } else {
            log.debug("方法不在包含列表中，跳过日志记录: {}.{}.{}", packageName, className, methodName);
        }
        return shouldLog;
    }
    
    /**
     * 检查是否在排除列表中
     */
    private boolean isInExcludeList(String packageName, String className, String methodName) {
        List<String> excludePackages = loggingConfig.getExcludePackages();
        List<String> excludeClasses = loggingConfig.getExcludeClasses();
        List<String> excludeMethods = loggingConfig.getExcludeMethods();
        
        if (excludePackages != null && excludePackages.stream().anyMatch(packageName::startsWith)) {
            return true;
        }
        
        if (excludeClasses != null && excludeClasses.contains(className)) {
            return true;
        }
        
        if (excludeMethods != null && excludeMethods.contains(methodName)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否在包含列表中
     */
    private boolean isInIncludeList(String packageName, String className, String methodName) {
        List<String> packages = loggingConfig.getPackages();
        List<String> classes = loggingConfig.getClasses();
        List<String> methods = loggingConfig.getMethods();
        
        // 如果没有配置包含列表，则默认包含所有
        if ((packages == null || packages.isEmpty()) && 
            (classes == null || classes.isEmpty()) && 
            (methods == null || methods.isEmpty())) {
            return true;
        }
        
        if (packages != null && packages.stream().anyMatch(packageName::startsWith)) {
            return true;
        }
        
        if (classes != null && classes.contains(className)) {
            return true;
        }
        
        if (methods != null && methods.contains(methodName)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 打印日志信息
     */
    private void printLog(LogInfo logInfo) {
        try {
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("\n========== AOP日志记录 ==========\n");
            logMessage.append("包名: ").append(logInfo.getPackageName()).append("\n");
            logMessage.append("类名: ").append(logInfo.getClassName()).append("\n");
            logMessage.append("方法名: ").append(logInfo.getMethodName()).append("\n");

            if (logInfo.getDescription() != null && !logInfo.getDescription().isEmpty()) {
                logMessage.append("描述: ").append(logInfo.getDescription()).append("\n");
            }

            if (logInfo.getArgs() != null && logInfo.getArgs().length > 0) {
                logMessage.append("请求参数: ").append(safeSerialize(logInfo.getArgs())).append("\n");
            }

            if (logInfo.getResult() != null) {
                logMessage.append("返回结果: ").append(safeSerialize(logInfo.getResult())).append("\n");
            }

            if (logInfo.getExecutionTime() > 0) {
                logMessage.append("执行时间: ").append(logInfo.getExecutionTime()).append("ms\n");
            }

            logMessage.append("执行状态: ").append(logInfo.isSuccess() ? "成功" : "失败").append("\n");

            if (!logInfo.isSuccess() && logInfo.getErrorMessage() != null) {
                logMessage.append("错误信息: ").append(logInfo.getErrorMessage()).append("\n");
            }

            logMessage.append("记录时间: ").append(logInfo.getTimestamp()).append("\n");
            logMessage.append("================================\n");

            if (logInfo.isSuccess()) {
                log.info(logMessage.toString());
            } else {
                log.error(logMessage.toString());
            }

        } catch (Exception e) {
            log.error("日志记录失败", e);
        }
    }

    /**
     * 清理参数，移除不可序列化的对象
     */
    private Object sanitizeArgument(Object arg) {
        if (arg == null) {
            return null;
        }

        // 处理MultipartFile
        if (arg instanceof MultipartFile) {
            MultipartFile file = (MultipartFile) arg;
            return "MultipartFile: {name=" + file.getOriginalFilename() +
                   ", size=" + file.getSize() +
                   ", contentType=" + file.getContentType() + "}";
        }

        // 处理MultipartFile数组
        if (arg instanceof MultipartFile[]) {
            MultipartFile[] files = (MultipartFile[]) arg;
            return Arrays.stream(files)
                        .map(file -> "MultipartFile: {name=" + file.getOriginalFilename() +
                                     ", size=" + file.getSize() +
                                     ", contentType=" + file.getContentType() + "}")
                        .toArray(String[]::new);
        }

        // 处理Logger对象
        if (arg.getClass().getName().contains("Logger")) {
            return "Logger: " + arg.getClass().getSimpleName();
        }

        // 处理其他不可序列化的对象
        if (isNonSerializable(arg)) {
            return arg.getClass().getSimpleName() + "@" + Integer.toHexString(arg.hashCode());
        }

        return arg;
    }

    /**
     * 检查对象是否为不可序列化的类型
     */
    private boolean isNonSerializable(Object obj) {
        if (obj == null) {
            return false;
        }

        String className = obj.getClass().getName();

        // 检查已知的不可序列化类型
        return className.contains("Logger") ||
               className.contains("MessageFactory") ||
               className.contains("LoggerContext") ||
               className.contains("Configuration") ||
               className.contains("Appender") ||
               className.contains("Layout") ||
               className.contains("Filter");
    }

    /**
     * 安全序列化对象，避免序列化异常
     */
    private String safeSerialize(Object obj) {
        if (obj == null) {
            return "null";
        }

        try {
            // 尝试使用专门的日志ObjectMapper序列化
            return loggingObjectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            // 如果Jackson序列化失败，使用安全的toString方法
            log.debug("Jackson序列化失败，使用toString方法: {}", e.getMessage());
            return safeToString(obj);
        }
    }

    /**
     * 安全的toString方法，处理各种类型的对象
     */
    private String safeToString(Object obj) {
        if (obj == null) {
            return "null";
        }

        try {
            // 处理数组
            if (obj.getClass().isArray()) {
                if (obj instanceof Object[]) {
                    Object[] array = (Object[]) obj;
                    StringBuilder sb = new StringBuilder("[");
                    for (int i = 0; i < array.length; i++) {
                        if (i > 0) sb.append(", ");
                        sb.append(safeToString(array[i]));
                    }
                    sb.append("]");
                    return sb.toString();
                } else {
                    return Arrays.toString((Object[]) obj);
                }
            }

            // 处理集合
            if (obj instanceof List) {
                List<?> list = (List<?>) obj;
                StringBuilder sb = new StringBuilder("[");
                for (int i = 0; i < list.size(); i++) {
                    if (i > 0) sb.append(", ");
                    sb.append(safeToString(list.get(i)));
                }
                sb.append("]");
                return sb.toString();
            }

            // 处理基本类型和字符串
            if (obj instanceof String || obj instanceof Number || obj instanceof Boolean) {
                return obj.toString();
            }

            // 处理其他对象，限制长度避免日志过长
            String result = obj.toString();
            if (result.length() > 1000) {
                return result.substring(0, 1000) + "...(truncated)";
            }
            return result;

        } catch (Exception e) {
            // 如果toString也失败，返回类型信息
            return obj.getClass().getSimpleName() + "@" + Integer.toHexString(obj.hashCode()) +
                   " (toString failed: " + e.getMessage() + ")";
        }
    }
}