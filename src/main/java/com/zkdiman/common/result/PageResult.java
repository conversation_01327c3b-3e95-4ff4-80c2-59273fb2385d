package com.zkdiman.common.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 封装分页查询结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageResult implements Serializable {

    @ApiModelProperty(value = "总页数")
    private long total; //总记录数

    @ApiModelProperty(value = "当前页数据集合")
    private List<Object> rows; //当前页数据集合

}