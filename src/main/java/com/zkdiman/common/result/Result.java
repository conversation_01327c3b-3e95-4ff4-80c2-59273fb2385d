package com.zkdiman.common.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 后端统一返回结果
 * @param <T>
 */
@Data
public class Result<T> implements Serializable {

    private Integer code; //编码：1成功，0和其它数字为失败
    private String msg; //错误信息
    private T data; //数据
    private Boolean success = false;//是否成功,默认为false

    public static <T> Result<T> success() {
        return success(null, "");
    }

    public static <T> Result<T> success(T object) {
        return success(object, "");
    }

    public static <T> Result<T> success(String msg) {
        return success(null, msg);
    }


    public static <T> Result<T> success(T object,String msg) {
        Result<T> result = new Result<T>();
        result.msg = msg;
        result.data = object;
        result.code = 1;
        result.success = true;
        return result;
    }

    public static <T> Result<T> error(String msg) {
        Result result = new Result();
        result.msg = msg;
        result.code = 0;
        return result;
    }

    public static <T> Result<T> error() {
        Result result = new Result();
        result.code = 0;
        return result;
    }
}