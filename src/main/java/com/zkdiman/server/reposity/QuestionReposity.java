package com.zkdiman.server.reposity;

import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Repository
public interface QuestionReposity extends MongoRepository<CompetitionQuestionLog, String> {

    /*根据准考证考查询图片地址*/
    @Query(value = "{ 'examCard': ?0 }", fields = "{ 'screenShot': 1, '_id': 0 }")
    CompetitionQuestionLog findScreenShotEntityByExamCard(String examCard);
    
    /*根据准考证考查询图片地址 - 直接返回数组*/
    default ArrayList<Answer> findScreenShotByExamCard(String examCard) {
        CompetitionQuestionLog result = findScreenShotEntityByExamCard(examCard);
        return result != null ? result.getStudentAnswer() : null;
    }
    
    /*根据准考证号添加一个新的图片地址到数组*/
    @Query("{ 'examCard': ?0 }")
    @Update("{ '$push': { 'screenShot': ?1 } }")
    void addScreenShotUrl(String examCard, String screenShotUrl);
    
    /*根据准考证号删除包含特定文件名的图片地址*/
    @Query("{ 'examCard': ?0 }")
    @Update("{ '$pull': { 'screenShot': { $regex: ?1 } } }")
    void removeScreenShotUrlContaining(String examCard, String fileNamePattern);

    /*根据competitionID获取学生考试截图（studentAnswer）数组，以及对应的学生准考证号*/
    @Query(value = "{ 'competitionId': ?0 }", fields = "{ 'studentAnswer': 1, '_id': 0 }")
    ArrayList<ArrayList<String>> findStudentAnswerByCompetitionId(String competitionId);
    
    /*根据competitionID获取所有学生的准考证号和截图URL*/
    @Query(value = "{ 'competitionId': ?0 }", fields = "{ 'examCard': 1, 'screenShot': 1, '_id': 0 }")
    List<CompetitionQuestionLog> findAllScreenShotsByCompetitionId(String competitionId);

    CompetitionQuestionLog findByExamCard(String examCard);
}
