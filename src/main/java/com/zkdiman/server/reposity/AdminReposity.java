package com.zkdiman.server.reposity;

import com.zkdiman.pojo.entity.Admin;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AdminReposity extends MongoRepository<Admin,String> {
    //根据用户名与密码查询是否存在该管理员
    Admin findByUsernameAndPassword(String username,String password);

    // 根据用户名查询管理员 (用于检查用户名唯一性)
    Admin findByUsername(String username);
}
