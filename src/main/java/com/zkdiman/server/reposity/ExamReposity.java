package com.zkdiman.server.reposity;

import com.zkdiman.pojo.entity.Exam;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ExamReposity extends MongoRepository<Exam, String> {
    // Basic CRUD and findById are provided by MongoRepository
    // For complex queries like pagination with optional filters, 
    // it's often better to use MongoTemplate in the service layer or define custom methods here.
} 