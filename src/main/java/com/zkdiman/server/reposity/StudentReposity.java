package com.zkdiman.server.reposity;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;
import org.springframework.stereotype.Repository;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;

@Repository
public interface StudentReposity extends MongoRepository<CompetitionSignUpLog, String> {
    /*根据准考证号查询是否存在学生*/
    CompetitionSignUpLog findByExamCard(String examCard);
    
    /*根据准考证号查询学生信息（只返回登录所需字段）*/
    @Cacheable(value = "studentLoginCache", key = "#examCard", unless = "#result == null")
    @Query(value = "{ 'examCard': ?0 }", fields = "{ 'name': 1, 'examCard': 1, 'school': 1, 'startTime': 1, 'duration': 1, 'submitType': 1,'grade': 1, '_id': 0 }")
    CompetitionSignUpLog findLoginInfoByExamCard(String examCard);
    
    /*根据准考证号检查学生是否存在*/
    @Cacheable(value = "studentExistsCache", key = "#examCard")
    @Query(value = "{ 'examCard': ?0 }", exists = true)
    boolean existsByExamCard(String examCard);

    /*根据准考证号更新学生提交考试状态*/
    @CacheEvict(value = {"studentLoginCache", "studentExistsCache"}, key = "#examCard")
    @Query("{ 'examCard' : ?0 }")
    @Update("{ '$set' : { 'submitType' : ?1 } }")
    void updateSubmitType(String examCard, Integer submitType);

    /*根据准考证号查询学生姓名*/
    @Cacheable(value = "studentLoginCache", key = "'name:'+#examCard", unless = "#result == null")
    @Query(value = "{ 'examCard': ?0 }", fields = "{ 'name': 1, '_id': 0 }")
    String findNameByExamCard(String examCard);

}
