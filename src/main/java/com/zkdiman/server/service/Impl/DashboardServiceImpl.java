package com.zkdiman.server.service.Impl;

import com.zkdiman.common.enums.ExamStatusEnum;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.vo.admin.DashboardSummaryVO;
import com.zkdiman.server.service.DashboardService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

@Service
public class DashboardServiceImpl implements DashboardService {

    private static final Logger logger = LogManager.getLogger(DashboardServiceImpl.class);

    @Autowired
    private MongoDBUtils mongoDBUtils;

    @Override
    public DashboardSummaryVO getDashboardSummary() {
        logger.info("Service: 获取仪表盘概要统计");

        // 获取学生总数
        long studentCount = mongoDBUtils.count(new Criteria(), CompetitionSignUpLog.class);

        // 获取总考试场次数
        long examCount = mongoDBUtils.count(new Criteria(), Exam.class);

        // 获取待开始的考试场次数
        long pendingCount = mongoDBUtils.count(Criteria.where("status").is(ExamStatusEnum.PENDING.getStatus()), Exam.class);

        // 获取进行中的考试场次数
        long ongoingCount = mongoDBUtils.count(Criteria.where("status").is(ExamStatusEnum.ONGOING.getStatus()), Exam.class);

        // 获取已结束的考试场次数
        long completedCount = mongoDBUtils.count(Criteria.where("status").is(ExamStatusEnum.COMPLETED.getStatus()), Exam.class);

        // 构建并返回VO
        return DashboardSummaryVO.builder()
                .studentCount(studentCount)
                .examCount(examCount)
                .pendingCount(pendingCount)
                .ongoingCount(ongoingCount)
                .completedCount(completedCount)
                .build();
    }
} 