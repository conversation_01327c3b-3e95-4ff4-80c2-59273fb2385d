package com.zkdiman.server.service.Impl;

import com.zkdiman.common.utils.ExportUtils;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.ExportTask;
import com.zkdiman.server.service.AsyncExportTaskService;
import com.zkdiman.server.service.strategy.export.ExportContext;
import com.zkdiman.server.service.strategy.export.ExportStrategy;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zkdiman.common.constant.TaskConstant.*;

/**
 * 异步导出任务服务实现
 * 专门处理异步导出任务的执行，确保真正的异步执行
 */
@Service
public class AsyncExportTaskServiceImpl implements AsyncExportTaskService {
    
    private static final Logger logger = LogManager.getLogger(AsyncExportTaskServiceImpl.class);

    @Autowired
    private List<ExportStrategy> exportStrategies;

    // 策略映射，用于快速查找对应的导出策略
    private Map<String, ExportStrategy> strategyMap;

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        // 构建策略映射
        strategyMap = exportStrategies.stream()
                .collect(Collectors.toMap(
                        ExportStrategy::getSupportedTaskType,
                        Function.identity()
                ));

        logger.info("异步导出服务策略初始化完成，支持的任务类型: {}", strategyMap.keySet());
    }
    
    /**
     * 异步执行导出任务
     * 使用策略模式根据任务类型选择对应的导出策略
     */
    @Async("exportTaskExecutor") // 使用专门的线程池
    @Override
    public void executeExportTaskAsync(String taskId, String examId, String taskType, Exam exam, ExportTask exportTask) {
        logger.info("AsyncService: 开始异步处理导出任务 {}, 考试场次 ID: {}, 任务类型: {}, 线程: {}", 
                   taskId, examId, taskType, Thread.currentThread().getName());

        // 更新任务状态为处理中
        ExportUtils.updateTaskStatus(taskId, TASK_STATUS_PROCESSING, null, null, null);

        try {
            // 获取对应的导出策略
            ExportStrategy strategy = strategyMap.get(taskType);
            Optional.ofNullable(strategy)
                    .orElseThrow(() -> new IllegalArgumentException("不支持的导出任务类型: " + taskType));

            logger.info("AsyncService: 任务 {} 使用策略: {}", taskId, strategy.getClass().getSimpleName());

            // 创建导出上下文
            ExportContext context = new ExportContext(taskId, examId, taskType, exam, exportTask);

            // 执行导出策略
            ExportStrategy.ExportResult result = strategy.execute(context);

            // 更新任务状态为完成
            ExportUtils.updateTaskStatus(taskId, TASK_STATUS_COMPLETED, result.getFileUrl(), result.getFileName(), null);

            logger.info("AsyncService: 导出任务 {} 执行完成，文件: {}, 线程: {}", 
                       taskId, result.getFileName(), Thread.currentThread().getName());

        } catch (Exception e) {
            logger.error("AsyncService: 处理导出任务 {} (考试场次 ID: {}) 时发生严重错误: {}, 线程: {}", 
                        taskId, examId, e.getMessage(), Thread.currentThread().getName(), e);
            ExportUtils.updateTaskStatus(taskId, TASK_STATUS_FAILED, null, null, "导出处理失败: " + e.getMessage());
        }
    }
}
