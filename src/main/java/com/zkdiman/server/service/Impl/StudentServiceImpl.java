package com.zkdiman.server.service.Impl;

import com.zkdiman.common.exception.*;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.entity.*;
import com.zkdiman.pojo.vo.student.StudentQueryVO;
import com.zkdiman.pojo.vo.student.TaskVO;
import com.zkdiman.server.reposity.QuestionReposity;
import com.zkdiman.server.service.QuestionLogInitService;
import com.zkdiman.server.service.AnswerSubmissionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import com.zkdiman.pojo.vo.student.StudentLoginVo;
import com.zkdiman.server.reposity.StudentReposity;
import com.zkdiman.server.service.StudentService;
import org.springframework.web.multipart.MultipartFile;
import com.zkdiman.common.result.PageResult;
import com.zkdiman.pojo.dto.student.StudentPageDTO;
import com.zkdiman.pojo.vo.student.StudentPageVO;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import com.zkdiman.pojo.dto.student.StudentScreenshotSubmitDTO;
import org.springframework.util.StringUtils;
import com.zkdiman.pojo.dto.student.SelectableStudentsQueryDTO;
import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.CompletableFuture;
import java.util.HashMap;
import java.util.Map;

import static com.zkdiman.common.utils.ExcelUtils.getCellStringValue;


@Service
public class StudentServiceImpl implements StudentService {
    private static final Logger logger = LogManager.getLogger(StudentServiceImpl.class);

    @Autowired
    private StudentReposity studentReposity;

    @Autowired
    private MongoDBUtils mongoDBUtils;

    @Autowired
    private QuestionReposity questionReposity;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private QuestionLogInitService questionLogInitService;

    @Autowired
    private AnswerSubmissionService answerSubmissionService;

    /**
     * 登录
     *
     * @param examCard: 准考证号
     * @return StudentLoginVo 同步结果
     */
    @Override
    public StudentLoginVo login(String examCard) {
        // Use optimized query that only returns fields needed for login
        CompetitionSignUpLog competitionSignUpLog = studentReposity.findLoginInfoByExamCard(examCard);

        //如果查询返回为空不用做后续操作了，直接返回空
        if (competitionSignUpLog == null) {
            return null;
        }

        if(competitionSignUpLog.getSubmitType()!=null){//SubmitType!=null表示已经提交过答题记录（学生主动提交或者超时自动提交）
            throw new BusinessException("该考生以提交答题记录，无法再次登录!");
        }

        StudentLoginVo studentLoginVo = new StudentLoginVo();
        //1.获取剩余时间（剩余时间等于考试开始时间减去当前时间）
        Long startTime = competitionSignUpLog.getStartTime();
        //获取考试开始时间,如果为空，则返回一个业务异常：暂未设置开考时间
        if (startTime == null) {
            throw new TimeException("暂未设置开考时间");
        }
        //判断当前时间是否已经超过考试结束时间，如果超过，则返回一个业务异常：考试已经结束
        if (competitionSignUpLog.getDuration() == null) { // Added null check for duration
            throw new TimeException("考试时长未设置");
        }
        if (startTime + competitionSignUpLog.getDuration() * 60 * 1000 < System.currentTimeMillis()) {
            throw new TimeException("考试已经结束");
        }
        //如果当前时间还没到考试开始时间，先让学生进入考试，未到考试时间的逻辑交由前端去处理

        //获取距离考试开始剩余开始时间，距离考试开始剩余时间=开始时间-当前时间
        studentLoginVo.setRemainStartTime(startTime - System.currentTimeMillis());

        //获取距离考试结束剩余时间，剩余时间=开始时间+考试时长*60*1000-当前时间
        studentLoginVo.setRemainEndTime(startTime + competitionSignUpLog.getDuration() * 60 * 1000 - System.currentTimeMillis());

        //获取结束考试时间，结束时间=开始时间+考试时长*60*1000
        studentLoginVo.setEndDate(startTime + competitionSignUpLog.getDuration() * 60 * 1000);

        //说明当前时间正在考试,此时可以对属性进行赋值
        studentLoginVo.setName(competitionSignUpLog.getName());
        studentLoginVo.setExamCard(competitionSignUpLog.getExamCard());
        studentLoginVo.setSchool(competitionSignUpLog.getSchool());
        studentLoginVo.setStartDate(startTime);
        //将考试持续时间的单位由分钟转换成时间戳返回给前端
        studentLoginVo.setDuration(competitionSignUpLog.getDuration() * 60 * 1000);

       // 查询考试任务列表taskList在CompetitionQuestionLog是否为空或者不存在，如果存在直接返回存在的，如果不存在则创建一个新的考试任务列表，并将考试列表分配至学生答题集合（CompetitionQuestionLog）
        CompetitionQuestionLog questionLog = questionReposity.findByExamCard(examCard);
        if (questionLog != null && questionLog.getTaskList() != null && !questionLog.getTaskList().isEmpty()) {
            // 如果任务列表在CompetitionQuestionLog表中已存在，直接设置到StudentLoginVo中
            //(不能使用beanUtils.copyProperties，因为TaskVO和ExamTask是不同的类型)
            //1.BeanUtils.copyProperties() 不能直接复制 List 类型的属性
            //2.源对象 questionLog.getTaskList() 是 List<ExamTask> 类型，目标对象 taskList 是 List<TaskVO> 类型，需要手动转换
            List<TaskVO> taskList = questionLog.getTaskList().stream()
                    .map(examTask -> {
                        TaskVO taskVO = new TaskVO();
                        BeanUtils.copyProperties(examTask, taskVO);
                        return taskVO;
                    })
                    .collect(Collectors.toList());
            studentLoginVo.setTaskList(taskList);
        } else {
            // 如果任务列表不存在，则创建新的任务列表，并且将其分配给学生答题记录
            // 获取考试任务列表
            //1.根据年级判断获取哪些考试任务列表
            String grade = competitionSignUpLog.getGrade();
            if (grade == null || grade.trim().isEmpty()) {
                throw new BusinessException("学生年级信息不能为空");
            }

            // 解析年级，支持中文和数字格式
            Criteria criteria = Criteria.where("type").is(Integer.parseInt(grade.trim()) >= 10 ? "0" : "1");

            List<ExaminationTasks> taskList = mongoDBUtils.find(criteria, ExaminationTasks.class);
            //2.打乱taskList元素的顺序
            if (taskList != null && !taskList.isEmpty()) {
                Collections.shuffle(taskList); // 随机打乱列表顺序
            }
            //3.拷贝任务列表到StudentLoginVo中
            List<TaskVO> taskVOList = Objects.requireNonNull(taskList).stream().map(task -> {
                TaskVO taskVO = new TaskVO();
                // 设置任务名称
                BeanUtils.copyProperties(task, taskVO);
                return taskVO;
            }).collect(Collectors.toList());

//            for (int i = 0; i < taskVOList.size(); i++) {
//                taskVOList.get(i).setName("任务" + (i + 1)); // 设置任务名称
//            }
            //3.设置任务列表到StudentLoginVo中
            studentLoginVo.setTaskList(taskVOList);

            //4.创建或更新学生答题记录
            if (questionLog == null) {
                // 创建新的答题记录
                questionLog = new CompetitionQuestionLog();
                questionLog.setExamCard(examCard);
                questionLog.setExamId(competitionSignUpLog.getExamID());
                logger.info("为学生 {} 创建新的答题记录", examCard);
            }

            // 将任务列表设置到学生答题记录中
            List<ExamTask> examTasks = taskVOList.stream().map(taskVO -> {
                ExamTask examTask = new ExamTask();
                BeanUtils.copyProperties(taskVO, examTask);
                return examTask;
            }).collect(Collectors.toList());

            questionLog.setTaskList(examTasks);

            // 保存学生答题记录表
            CompetitionQuestionLog savedQuestionLog = mongoDBUtils.save(questionLog);
            if (savedQuestionLog != null) {
                logger.info("学生 {} 的答题记录已保存，包含 {} 个任务", examCard, examTasks.size());
            } else {
                logger.error("学生 {} 的答题记录保存失败", examCard);
                throw new BusinessException("答题记录保存失败");
            }
        }

        return studentLoginVo;
    }

    /**
     * 学生交卷
     *
     * @param examCard   准考证号
     * @param submitType 0表示未提交，1表示已提交
     */
    @Async("studentLoginExecutor")
    @Override
    public CompletableFuture<String> completed(String examCard, Integer submitType) {
        String name = studentReposity.findNameByExamCard(examCard);
        studentReposity.updateSubmitType(examCard, submitType);
        return CompletableFuture.completedFuture(name);
    }

    /**
     * 学生列表分页查询
     *
     * @param studentPageDto 学生列表分页查询条件
     * @return PageResult 学生列表分页结果
     */
    public PageResult getStudentListPage(StudentPageDTO studentPageDto) {
        Criteria criteria = new Criteria();
        if (StringUtils.hasText(studentPageDto.getExamCard())) {
            criteria.and("examCard").is(studentPageDto.getExamCard());
        }
        if (StringUtils.hasText(studentPageDto.getName())) {
            criteria.and("name").regex(".*" + studentPageDto.getName() + ".*", "i");
        }
        if (StringUtils.hasText(studentPageDto.getSchool())) {
            criteria.and("school").regex(".*" + studentPageDto.getSchool() + ".*", "i");
        }

        // 从 DTO 中获取分页参数，如果未提供，则使用默认值
        int page = studentPageDto.getPage() - 1; // MongoDBUtils 中 page 从0开始
        int pageSize = studentPageDto.getPageSize();

        Page<CompetitionSignUpLog> studentPage = mongoDBUtils.findPage(
                criteria,
                page,
                pageSize,
                "examCard", // 默认按准考证号排序
                Sort.Direction.ASC,
                CompetitionSignUpLog.class
        );

        List<StudentPageVO> studentPageVOList = studentPage.getContent().stream().map(competitionSignUpLog -> {
            StudentPageVO vo = StudentPageVO.builder().build();
            BeanUtils.copyProperties(competitionSignUpLog, vo);
            // 查询学校信息
            School school = mongoDBUtils.findOne(
                    Criteria.where("schoolCode").is(competitionSignUpLog.getSchoolCode()),
                    School.class
            );
            vo.setSchool(school);
            return vo;
        }).collect(Collectors.toList());

        return new PageResult(studentPage.getTotalElements(), new ArrayList<>(studentPageVOList));
    }

    /**
     * 根据id获取学生信息
     *
     * @param id 学生id
     * @return 学生信息
     * @throws EntityNotFoundException if student is not found
     */
    public StudentQueryVO getStudentById(String id) {
        logger.info("Service: 根据ID查询学生信息, ID: {}", id);

        // Use findById for more direct ID-based querying
        CompetitionSignUpLog competitionSignUpLog = mongoDBUtils.findById(id, CompetitionSignUpLog.class);

        if (competitionSignUpLog == null) {
            throw new EntityNotFoundException("Student not found with id: " + id);
        }

        StudentQueryVO studentQueryVO = new StudentQueryVO();
        BeanUtils.copyProperties(competitionSignUpLog, studentQueryVO);

        // 查询学校信息 - 与分页查询保持一致
        if (StringUtils.hasText(competitionSignUpLog.getSchoolCode())) {
            School school = mongoDBUtils.findOne(
                    Criteria.where("schoolCode").is(competitionSignUpLog.getSchoolCode()),
                    School.class
            );
            studentQueryVO.setSchool(school);

            logger.debug("学生 {} (ID: {}) 的学校信息: {}",
                        competitionSignUpLog.getName(), id,
                        school != null ? school.getSchoolName() : "未找到");
        } else {
            logger.warn("学生 {} (ID: {}) 没有学校代码", competitionSignUpLog.getName(), id);
        }

        return studentQueryVO;
    }

    /**
     * 添加或更新学生信息
     *
     * @param studentQueryVO 学生信息
     */
    @CacheEvict(value = {"studentLoginCache", "studentExistsCache"}, key = "#studentQueryVO.examCard")
    public void addOrUpdateStudent(StudentQueryVO studentQueryVO) {
        logger.info("Service: 添加或更新学生信息, 学生ID: {}, 准考证号: {}",
                   studentQueryVO.getID(), studentQueryVO.getExamCard());

        // 1. 处理学校信息 - 不存在则添加，存在则更新
        School school = studentQueryVO.getSchool();
        if (school != null && StringUtils.hasText(school.getSchoolCode())) {
            handleSchoolInfo(school);
            // 设置学生的学校代码关联
            studentQueryVO.setSchoolCode(school.getSchoolCode());
        }

        // 2. 处理学生信息
        CompetitionSignUpLog competitionSignUpLog;
        boolean isUpdate = StringUtils.hasText(studentQueryVO.getID());

        if (isUpdate) {
            // 更新现有学生
            competitionSignUpLog = mongoDBUtils.findById(studentQueryVO.getID(), CompetitionSignUpLog.class);
            if (competitionSignUpLog == null) {
                throw new EntityNotFoundException("未找到ID为 " + studentQueryVO.getID() + " 的学生");
            }
            logger.info("更新学生信息: {}", studentQueryVO.getName());
        } else {
            // 创建新学生
            competitionSignUpLog = new CompetitionSignUpLog();
            logger.info("创建新学生: {}", studentQueryVO.getName());
        }

        // 复制属性（排除school对象，因为我们只需要schoolCode）
        BeanUtils.copyProperties(studentQueryVO, competitionSignUpLog);
        competitionSignUpLog.setSchool(studentQueryVO.getSchool().getSchoolName());

        // 保存学生信息
        CompetitionSignUpLog savedStudent = mongoDBUtils.save(competitionSignUpLog);
        if (savedStudent == null) {
            throw new EntityAddException("学生信息保存失败，ID: " + studentQueryVO.getID());
        }

        logger.info("学生信息保存成功: {} ({})", savedStudent.getName(),
                   isUpdate ? "更新" : "新增");

        // 如果学生已分配到考试场次，初始化答题记录
        if (StringUtils.hasText(savedStudent.getExamID())) {
            try {
                CompetitionQuestionLog questionLog = questionLogInitService.createOrUpdateQuestionLog(savedStudent);
                if (questionLog != null) {
                    logger.info("为学生 {} 初始化答题记录成功，考试ID: {}",
                               savedStudent.getExamCard(), savedStudent.getExamID());
                } else {
                    logger.warn("为学生 {} 初始化答题记录失败", savedStudent.getExamCard());
                }
            } catch (Exception e) {
                logger.error("为学生 {} 初始化答题记录时发生异常: {}",
                           savedStudent.getExamCard(), e.getMessage(), e);
                // 不抛出异常，避免影响学生信息保存
            }
        } else {
            logger.debug("学生 {} 未分配到考试场次，跳过答题记录初始化", savedStudent.getExamCard());
        }
    }

    /**
     * 处理学校信息 - 不存在则添加，存在则更新
     *
     * @param school 学校信息
     */
    private void handleSchoolInfo(School school) {
        if (school == null || !StringUtils.hasText(school.getSchoolCode())) {
            logger.warn("学校信息无效，跳过处理");
            return;
        }

        String schoolCode = school.getSchoolCode();
        logger.debug("处理学校信息: {}", schoolCode);

        // 查询学校是否已存在
        School existingSchool = mongoDBUtils.findOne(
                Criteria.where("schoolCode").is(schoolCode),
                School.class
        );

        if (existingSchool != null) {
            // 学校已存在，更新学校信息
            logger.info("学校已存在，更新学校信息: {} -> {}",
                       existingSchool.getSchoolName(), school.getSchoolName());

            // 保留原有ID，更新其他信息
            school.setID(existingSchool.getID());

            // 更新学校信息
            Criteria criteria = Criteria.where("schoolCode").is(schoolCode);

            Map<String, Object> schoolUpdateMap = new HashMap<>();
            schoolUpdateMap.put("schoolCode", school.getSchoolCode());
            schoolUpdateMap.put("schoolName", school.getSchoolName());
            schoolUpdateMap.put("contactNumber",school.getContactNumber());
            schoolUpdateMap.put("teacherNumber",school.getTeacherNumber());
            schoolUpdateMap.put("zoneName",school.getZoneName());

            boolean isupdated = mongoDBUtils.updateOne(criteria,schoolUpdateMap,School.class);
            if (!isupdated) {
                logger.error("更新学校信息失败: {}", schoolCode);
                throw new EntityAddException("更新学校信息失败: " + schoolCode);
            }

            logger.info("学校信息更新成功: {}", school.getSchoolName());
        } else {
            // 学校不存在，创建新学校
            logger.info("学校不存在，创建新学校: {}", school.getSchoolName());

            // 清除ID，让MongoDB自动生成
            school.setID(null);

            School savedSchool = mongoDBUtils.save(school);
            if (savedSchool == null) {
                logger.error("创建学校信息失败: {}", schoolCode);
                throw new EntityAddException("创建学校信息失败: " + schoolCode);
            }

            logger.info("学校信息创建成功: {} (ID: {})",
                       savedSchool.getSchoolName(), savedSchool.getID());
        }
    }

    /**
     * 根据id删除学生
     *
     * @param id 学生id
     */
    public void deleteStudent(String id) {
        // 先查询学生信息获取准考证号，用于清除缓存
        CompetitionSignUpLog student = mongoDBUtils.findById(id, CompetitionSignUpLog.class);
        if (student == null) {
            throw new EntityNotFoundException("未找到ID为 " + id + " 的学生");
        }

        String examCard = student.getExamCard();
        logger.info("Service: 删除学生 {} (准考证号: {})", student.getName(), examCard);

        Criteria criteria = new Criteria();
        criteria.and("_id").is(id);
        boolean istrue = mongoDBUtils.deleteOne(criteria, CompetitionSignUpLog.class);
        if (!istrue) {
            throw new EntityDeleteException("Student Delete Error with id: " + id);
        }

        // 手动清除缓存
        if (StringUtils.hasText(examCard)) {
            clearStudentCache(examCard);
            logger.info("已清除学生 {} 的缓存", examCard);
        }
    }

    /**
     * 批量导入学生账号
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @Override
    public BatchImportResultVO batchImportStudents(MultipartFile file) {
        if (file.isEmpty()) {
            throw new UploadException("上传的文件不能为空");
        }

        List<BatchImportResultVO.ImportError> errors = new ArrayList<>();
        List<CompetitionSignUpLog> studentsToSave = new ArrayList<>();
        List<School> schoolsToSave = new ArrayList<>(); // 如果需要保存学校信息
        int successCount = 0;
        int failureCount = 0;
        Workbook workbook;

        // 校验文件名称
        try (InputStream inputStream = file.getInputStream()) {
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.trim().isEmpty()) {
                throw new UploadException("文件名不能为空");
            }

            // 校验文件格式
            if (fileName.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (fileName.endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new UploadException("文件格式不支持，请上传Excel文件 (.xls或.xlsx)");
            }


            // 读取Excel文件
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            int firstDataRowIndex = 1; // 数据从Excel的第二行开始（0-indexed） 第一行为:表头信息

            // 检查表头
            for (int rowIndex = firstDataRowIndex; rowIndex <= sheet.getLastRowNum(); rowIndex++) { // 从第二行开始遍历
                Row currentRow = sheet.getRow(rowIndex);
                int currentExcelRowNum = rowIndex + 1; // 用于错误报告的实际Excel行号 (1-indexed)

                if (currentRow == null) {
                    // 如果行为null（可能是完全的空行），可以选择记录错误或直接跳过
                    // 为保持与之前逻辑相似，这里可以考虑添加一个错误记录或只是continue
                    // errors.add(BatchImportResultVO.ImportError.builder().rowNumber(currentExcelRowNum).identifier("N/A").reason("空白行").build());
                    // failureCount++;
                    continue; // 跳过空行
                }

                try {
                    // 创建一个新的学生对象用来存储当前行的数据
                    CompetitionSignUpLog student = new CompetitionSignUpLog();
                    // 创建一个新的学校对象用来存储学校信息
                    School school = new School();

                    // 0: 年级 (grade)
                    student.setGrade(getCellStringValue(currentRow.getCell(0), workbook));

                    // 1: 班级 (className) - Required
                    student.setClassName(getCellStringValue(currentRow.getCell(1), workbook));

                    // 2: 姓名 (name) - Required
                    String name = getCellStringValue(currentRow.getCell(2), workbook);
                    if (name == null || name.trim().isEmpty()) {
                        errors.add(BatchImportResultVO.ImportError.builder().rowNumber(currentExcelRowNum).identifier(student.getExamCard()).reason("姓名不能为空").build());
                        failureCount++;
                        continue;
                    }
                    student.setName(name.trim());

                    // 3: 性别 (gender)
                    student.setGender(Integer.valueOf(getCellStringValue(currentRow.getCell(3), workbook)));

                    // 4: 身份证号 (idCard)
                    student.setIdCard(getCellStringValue(currentRow.getCell(4), workbook));

                    // 5: 准考证号/学籍号 (examCard) - Required
                    String examCard = getCellStringValue(currentRow.getCell(5), workbook);
                    if (examCard == null || examCard.trim().isEmpty()) {
                        errors.add(BatchImportResultVO.ImportError.builder().rowNumber(currentExcelRowNum).identifier(examCard).reason("准考证号不能为空").build());
                        failureCount++;
                        continue;
                    }
                    student.setExamCard(examCard.trim());

                    // Check for duplicates before proceeding further for this row
                    if (studentReposity.findByExamCard(student.getExamCard()) != null) {
                        errors.add(BatchImportResultVO.ImportError.builder().rowNumber(currentExcelRowNum).identifier(student.getExamCard()).reason("准考证号已存在").build());
                        failureCount++;
                        continue;
                    }

                    // 6: 民族 (nation)
                    student.setNation(getCellStringValue(currentRow.getCell(6), workbook));

                    // 7: 就读方式 (studyMode)
                    student.setStudyMode(Integer.valueOf(getCellStringValue(currentRow.getCell(7), workbook)));

                    // 8: 户口本性质 (householdType)
                    student.setHouseholdType(Integer.valueOf(getCellStringValue(currentRow.getCell(8), workbook)));

                    //9.学校 (school)
                    student.setSchool(getCellStringValue(currentRow.getCell(9), workbook));
                    school.setSchoolName(getCellStringValue(currentRow.getCell(9), workbook));

                    // 10: 学校区名
                    school.setZoneName(getCellStringValue(currentRow.getCell(10), workbook));

                    // 11. 学校代码 (schoolCode)
                    student.setSchoolCode(getCellStringValue(currentRow.getCell(11), workbook));
                    school.setSchoolCode(getCellStringValue(currentRow.getCell(11), workbook));

                    // 12. 本科科学教师数 (teacherNumber)
                    school.setTeacherNumber(getCellStringValue(currentRow.getCell(12), workbook));

                    // 13. 学校负责人员的联系电话 (contactNumber)
                    school.setContactNumber(getCellStringValue(currentRow.getCell(13), workbook));

                    // 14: 考场 (examRoom)
                    student.setExamRoom(getCellStringValue(currentRow.getCell(14), workbook));

                    // 15: 考场号 (examRoomNumber)
                    student.setExamRoomNumber(getCellStringValue(currentRow.getCell(15), workbook));

                    // 16: 座位号 (seatNumber)
                    student.setSeatNumber(getCellStringValue(currentRow.getCell(16), workbook));

                    // 17: 考试地点 (examLocation)
                    student.setExamLocation(getCellStringValue(currentRow.getCell(17), workbook));

                    Integer[] scores = new Integer[5]; // Assuming 5 subjects for 10th grade
                    // 18: 语文成绩
                    Integer chineseScore = Integer.valueOf(getCellStringValue(currentRow.getCell(18), workbook));

                    // 19: 数学成绩
                    Integer mathScore = Integer.valueOf(getCellStringValue(currentRow.getCell(19), workbook));

                    // 20: 英语成绩
                    Integer englishScore = Integer.valueOf(getCellStringValue(currentRow.getCell(20), workbook));

                    // 21: 物理成绩
                    Integer physicsScore = Integer.valueOf(getCellStringValue(currentRow.getCell(21), workbook));

                    // 22: 政治成绩
                    Integer politicsScore = Integer.valueOf(getCellStringValue(currentRow.getCell(22), workbook));
                    // 将成绩存入数组
                    scores[0] = chineseScore;
                    scores[1] = mathScore;
                    scores[2] = englishScore;
                    scores[3] = physicsScore;
                    scores[4] = politicsScore;

                    // 保存学生信息
                    studentsToSave.add(student);

                    // 保存学校信息
                    schoolsToSave.add(school);
                } catch (Exception e) {
                    logger.error("处理Excel行 {} 失败: {}", currentExcelRowNum, e.getMessage(), e);
                    errors.add(BatchImportResultVO.ImportError.builder()
                            .rowNumber(currentExcelRowNum)
                            .identifier("N/A") // Could try to get examCard if available at this point
                            .reason("行数据处理异常: " + e.getMessage())
                            .build());
                    failureCount++;
                }
            }

            if (!studentsToSave.isEmpty()) {
                mongoDBUtils.saveAll(studentsToSave, CompetitionSignUpLog.class); // Or studentReposity.saveAll(studentsToSave);
                successCount = studentsToSave.size();

                // 为已分配到考试场次的学生初始化答题记录
                List<CompetitionSignUpLog> studentsWithExam = studentsToSave.stream()
                        .filter(student -> StringUtils.hasText(student.getExamID()))
                        .collect(Collectors.toList());

                if (!studentsWithExam.isEmpty()) {
                    logger.info("开始为 {} 个已分配考试场次的学生初始化答题记录", studentsWithExam.size());
                    try {
                        int initSuccessCount = questionLogInitService.batchInitQuestionLogsForStudents(studentsWithExam);
                        logger.info("批量初始化答题记录完成: {} / {} 成功", initSuccessCount, studentsWithExam.size());
                    } catch (Exception e) {
                        logger.error("批量初始化答题记录时发生异常: {}", e.getMessage(), e);
                        // 不影响学生导入的成功状态
                    }
                } else {
                    logger.debug("批量导入的学生中无已分配考试场次的学生，跳过答题记录初始化");
                }
            }

            if (!schoolsToSave.isEmpty()) {
                // 去重处理：按schoolCode去重，避免重复创建相同学校
                Map<String, School> uniqueSchools = new HashMap<>();
                for (School school : schoolsToSave) {
                    if (StringUtils.hasText(school.getSchoolCode())) {
                        uniqueSchools.put(school.getSchoolCode(), school);
                    }
                }

                // 遍历每个唯一学校，根据 schoolCode 更新或新增
                for (School school : uniqueSchools.values()) {
                    try {
                        handleSchoolInfo(school);
                    } catch (Exception e) {
                        logger.error("批量导入时处理学校信息失败: {} - {}",
                                   school.getSchoolCode(), e.getMessage());
                        // 继续处理其他学校，不中断整个导入过程
                    }
                }

                logger.info("批量导入处理了 {} 个唯一学校", uniqueSchools.size());
            }

        } catch (UploadException ue) {
            throw ue;
        } catch (Exception e) {
            logger.error("批量导入学生失败: {}", e.getMessage(), e);
            throw new BatchImportException("批量导入过程中发生错误: " + e.getMessage());
        }
        //表头导入错误不算错误
        failureCount--;
        return BatchImportResultVO.builder()
                .successCount(successCount)
                .failureCount(failureCount)
                .errors(errors)
                .build();
    }

    /**
     * 根据准考证号提交答题截图URL,题号，题目类型，分数（主观题）,题目选项
     *
     * @param submitDTO 包含准考证号和截图URL的DTO
     */
    @Async("studentLoginExecutor")
    @Override
    public CompletableFuture<Void> submitScreenshotUrl(StudentScreenshotSubmitDTO submitDTO) {
        // Log the submission attempt
        Answer answer = submitDTO.getAnswer();
        if (answer == null) {
            logger.error("提交答题数据失败：学生 {} 提交的答案内容为空。", submitDTO.getExamCard());
            return CompletableFuture.failedFuture(new BusinessException("提交失败：答案内容不能为空。"));
        }

        logger.info("学生 {} 提交答题数据: 题目类型 {}, 题目ID {}, 截图URL: {},分数: {}, 选项: {}",
                submitDTO.getExamCard(),
                answer.getQuestionType(),
                answer.getQuestionId(),
                answer.getUrl(),
                answer.getScore(),
                answer.getOption());

        // First, verify the student exists using an optimized exists query
        if (!studentReposity.existsByExamCard(submitDTO.getExamCard())) {
            logger.error("提交答题数据失败：未找到准考证号为 {} 的学生报名记录。", submitDTO.getExamCard());
            return CompletableFuture.failedFuture(new BusinessException("提交失败：无效的准考证号或未报名当前考试。"));
        }

        // Check Whether the questionLog is empty
        CompetitionQuestionLog questionLog = questionReposity.findByExamCard(submitDTO.getExamCard());
        //  todo 查询CompetitionSignUpLog表获取学生对应的ExamID(考场id)
        CompetitionSignUpLog competitionSignUpLog = mongoDBUtils.findOne(Criteria.where("examCard").is(submitDTO.getExamCard()), CompetitionSignUpLog.class);
        if (questionLog == null) {
            // Create a new question log if it doesn't exist,and populate it with the answer
            questionLog = new CompetitionQuestionLog();
            questionLog.setExamCard(submitDTO.getExamCard());
            questionLog.setExamId(competitionSignUpLog.getExamID());
            // Get examID directly from query rather than loading full student object
            CompetitionSignUpLog signUpLog = mongoDBUtils.findOne(
                    Criteria.where("examCard").is(submitDTO.getExamCard()),
                    CompetitionSignUpLog.class
            );
            if (signUpLog != null && signUpLog.getExamID() != null) {
                questionLog.setExamId(signUpLog.getExamID());
            } else {
                logger.warn("无法为学生 {} 的答题记录设置 ExamId，报名信息中未找到 ExamID。", submitDTO.getExamCard());
            }

            // 初始化答题记录列表并添加第一个答案
            ArrayList<Answer> studentAnswers = answerSubmissionService.addOrUpdateAnswer(
                new ArrayList<>(), answer, submitDTO.getExamCard());
            questionLog.setStudentAnswer(studentAnswers);

            logger.info("学生 {} 创建新的答题记录，首个题目 {} (类型: {})",
                       submitDTO.getExamCard(), answer.getQuestionId(), answer.getQuestionType());
        } else {
            // Update the existing question log using the answer submission service
            ArrayList<Answer> updatedAnswers = answerSubmissionService.addOrUpdateAnswer(
                questionLog.getStudentAnswer(), answer, submitDTO.getExamCard());
            questionLog.setStudentAnswer(updatedAnswers);
        }
        questionReposity.save(questionLog); // Save the question log

        logger.info("学生 {} 的答题数据 (题目类型: {}) 已成功保存/更新。",
                submitDTO.getExamCard(), answer.getQuestionType());

        return CompletableFuture.completedFuture(null);
    }

    @Override
    public PageResult getSelectableStudents(SelectableStudentsQueryDTO queryDTO) {
        logger.info("Service: 查询可选学员列表, 参数: {}", queryDTO);

        Criteria criteria = new Criteria();

        if (StringUtils.hasText(queryDTO.getExamCard())) {
            criteria.and("examCard").is(queryDTO.getExamCard());
        }
        if (StringUtils.hasText(queryDTO.getName())) {
            criteria.and("name").regex(queryDTO.getName(), "i"); // Case-insensitive regex search
        }
        if (StringUtils.hasText(queryDTO.getSchool())) {
            criteria.and("school").regex(queryDTO.getSchool(), "i"); // Case-insensitive regex search
        }

        // Handle examID exclusion
        if (StringUtils.hasText(queryDTO.getExcludeExamId())) {
            // Students not in the specified exam OR not in any exam yet
            Criteria excludeCriteria = new Criteria().orOperator(
                    Criteria.where("examID").ne(queryDTO.getExcludeExamId()),
                    Criteria.where("examID").is(null) // or .exists(false)
            );
            criteria.andOperator(excludeCriteria); // Combine with other filters
        } else {
            // If no excludeExamId is given, by default, list only students not yet assigned to any exam
            criteria.and("examID").is(null); // or .exists(false)
        }

        // Page number is 0-indexed in Spring Data / MongoDBUtils
        Page<CompetitionSignUpLog> studentPage = mongoDBUtils.findPage(
                criteria,
                queryDTO.getPage() - 1,
                queryDTO.getPageSize(),
                "examCard", // Default sort field
                Sort.Direction.ASC, // Default sort direction
                CompetitionSignUpLog.class
        );

        // The response structure only requires ID, examCard, name.
        // For consistency with getStudentListPage which uses a VO for rows,
        // we could map to a simpler VO if needed. Or return CompetitionSignUpLog objects
        // and let frontend pick. The example showed minimal fields but PageResult<CompetitionSignUpLog> is okay.
        // For now, returning CompetitionSignUpLog directly in PageResult as PageResult expects List<Object>.
        return new PageResult(studentPage.getTotalElements(), new ArrayList<>(studentPage.getContent()));
    }

    @Override
    public ArrayList<Answer> getScreenshotsByExamCard(String examCard) {
        if (!StringUtils.hasText(examCard)) {
            throw new BusinessException("准考证号不能为空");
        }

        Criteria criteria = Criteria.where("examCard").is(examCard);
        CompetitionQuestionLog questionLogs = mongoDBUtils.findOne(criteria, CompetitionQuestionLog.class);

        if (questionLogs == null) {
            logger.info("No question logs found for examCard: {}", examCard);
            return new ArrayList<>(); // Return empty list
        }

        return questionLogs.getStudentAnswer();
    }

    /**
     * 手动清除指定学生的缓存
     *
     * @param examCard 准考证号
     */
    private void clearStudentCache(String examCard) {
        try {
            // 清除登录缓存
            if (cacheManager.getCache("studentLoginCache") != null) {
                cacheManager.getCache("studentLoginCache").evict(examCard);
                cacheManager.getCache("studentLoginCache").evict("name:" + examCard);
            }

            // 清除存在性检查缓存
            if (cacheManager.getCache("studentExistsCache") != null) {
                cacheManager.getCache("studentExistsCache").evict(examCard);
            }

            logger.debug("已清除学生 {} 的所有缓存", examCard);
        } catch (Exception e) {
            logger.warn("清除学生 {} 缓存时发生异常: {}", examCard, e.getMessage());
        }
    }

    /**
     * 清除所有学生相关缓存
     * 用于定时任务或手动清理
     */
    public void clearAllStudentCache() {
        try {
            // 清除所有登录缓存
            if (cacheManager.getCache("studentLoginCache") != null) {
                cacheManager.getCache("studentLoginCache").clear();
                logger.info("已清除所有学生登录缓存");
            }

            // 清除所有存在性检查缓存
            if (cacheManager.getCache("studentExistsCache") != null) {
                cacheManager.getCache("studentExistsCache").clear();
                logger.info("已清除所有学生存在性检查缓存");
            }

            logger.info("所有学生相关缓存已清除");
        } catch (Exception e) {
            logger.error("清除所有学生缓存时发生异常: {}", e.getMessage(), e);
        }
    }
}
