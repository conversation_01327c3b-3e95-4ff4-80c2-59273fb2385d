package com.zkdiman.server.service.Impl;

import com.zkdiman.common.constant.TaskConstant;
import com.zkdiman.common.exception.EntityNotFoundException;
import com.zkdiman.common.utils.ExportUtils;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.ExportTask;
import com.zkdiman.pojo.vo.admin.ExportTaskStatusVO;
import com.zkdiman.server.service.ExamExportService;
import com.zkdiman.server.service.AsyncExportTaskService;
import com.zkdiman.server.service.strategy.export.ExportStrategy;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zkdiman.common.constant.TaskConstant.*;

@Service
public class ExamExportServiceImpl implements ExamExportService {

    private static final Logger logger = LogManager.getLogger(ExamExportServiceImpl.class);

    @Autowired
    private MongoDBUtils mongoDBUtils;

    @Autowired
    public List<ExportStrategy> exportStrategies;

    @Autowired
    private AsyncExportTaskService asyncExportTaskService;

    // 策略映射，用于快速查找对应的导出策略
    private Map<String, ExportStrategy> strategyMap;

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        // 初始化ExportUtils中的MongoDBUtils
        ExportUtils.setMongoDBUtils(mongoDBUtils);

        // 构建策略映射
        strategyMap = exportStrategies.stream()
                .collect(Collectors.toMap(
                        ExportStrategy::getSupportedTaskType,
                        Function.identity()
                ));

        logger.info("导出策略初始化完成，支持的任务类型: {}", strategyMap.keySet());
    }

    @Override
    public String initiateExamScreenshotsExport(String examId) {
        logger.info("Service: 接收到考试场次 {} 的截图导出请求", examId);

        // 验证考试场次是否存在
        Exam exam = mongoDBUtils.findById(examId, Exam.class);

        Optional.ofNullable(exam)
                .orElseThrow(() -> {
                    logger.error("Service: 考试场次 {} 不存在，无法启动导出任务", examId);
                    return new EntityNotFoundException("考试场次不存在: " + examId);
                });

        // 生成任务ID并创建导出任务
        String taskId = UUID.randomUUID().toString();
        long currentTime = System.currentTimeMillis();

        ExportTask task = ExportTask.builder()
                .id(taskId)
                .examId(examId)
                .taskType(TaskConstant.TASK_TYPE_EXAM_SCREENSHOTS)
                .status(TASK_STATUS_PENDING)
                .createdAt(currentTime)
                .updatedAt(currentTime)
                .build();
        mongoDBUtils.save(task);
        logger.info("Service: 已创建截图导出任务 {}，状态: PENDING", taskId);

        // 异步执行导出任务 - 使用独立的异步服务
        asyncExportTaskService.executeExportTaskAsync(taskId, examId, TaskConstant.TASK_TYPE_EXAM_SCREENSHOTS, exam, task);

        logger.info("Service: 截图导出任务 {} 已提交到异步服务，立即返回任务ID", taskId);
        return taskId;
    }



    @Override
    public ExportTaskStatusVO getExportTaskStatus(String taskId) {
        logger.debug("Service: 查询导出任务状态, 任务ID: {}", taskId);
        ExportTask task = mongoDBUtils.findById(taskId, ExportTask.class);

        Optional.ofNullable(task)
                .orElseThrow(() -> new EntityNotFoundException("导出任务不存在: " + taskId));

        return ExportTaskStatusVO.builder()
                .taskId(task.getId())
                .examId(task.getExamId())
                .taskType(task.getTaskType())
                .status(task.getStatus())
                .fileUrl(task.getFileUrl())
                .fileName(task.getFileName())
                .errorMessage(task.getErrorMessage())
                .createdAt(task.getCreatedAt())
                .updatedAt(task.getUpdatedAt())
                .build();
    }

}
