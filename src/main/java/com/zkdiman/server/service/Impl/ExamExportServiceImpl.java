package com.zkdiman.server.service.Impl;

import com.zkdiman.common.constant.TaskConstant;
import com.zkdiman.common.exception.EntityNotFoundException;
import com.zkdiman.common.utils.ExportUtils;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.ExamLogExportDTO;
import com.zkdiman.pojo.entity.*;
import com.zkdiman.pojo.vo.admin.ExportTaskStatusVO;
import com.zkdiman.server.service.ExamExportService;
import com.zkdiman.server.service.AsyncExportTaskService;
import com.zkdiman.server.service.strategy.export.ExportStrategy;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zkdiman.common.constant.TaskConstant.*;

@Service
public class ExamExportServiceImpl implements ExamExportService {

    private static final Logger logger = LogManager.getLogger(ExamExportServiceImpl.class);

    @Autowired
    private MongoDBUtils mongoDBUtils;

    @Autowired
    public List<ExportStrategy> exportStrategies;

    @Autowired
    private AsyncExportTaskService asyncExportTaskService;

    @Autowired
    private  MongoTemplate mongoTemplate;
    // 假设有一个UserService可以根据准考证号查询学生信息
    // private final UserService userService;

    // 策略映射，用于快速查找对应的导出策略
    private Map<String, ExportStrategy> strategyMap;

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        // 初始化ExportUtils中的MongoDBUtils
        ExportUtils.setMongoDBUtils(mongoDBUtils);

        // 构建策略映射
        strategyMap = exportStrategies.stream()
                .collect(Collectors.toMap(
                        ExportStrategy::getSupportedTaskType,
                        Function.identity()
                ));

        logger.info("导出策略初始化完成，支持的任务类型: {}", strategyMap.keySet());
    }

    @Override
    public String initiateExamScreenshotsExport(String examId) {
        logger.info("Service: 接收到考试场次 {} 的截图导出请求", examId);

        // 验证考试场次是否存在
        Exam exam = mongoDBUtils.findById(examId, Exam.class);

        Optional.ofNullable(exam)
                .orElseThrow(() -> {
                    logger.error("Service: 考试场次 {} 不存在，无法启动导出任务", examId);
                    return new EntityNotFoundException("考试场次不存在: " + examId);
                });

        // 生成任务ID并创建导出任务
        String taskId = UUID.randomUUID().toString();
        long currentTime = System.currentTimeMillis();

        ExportTask task = ExportTask.builder()
                .id(taskId)
                .examId(examId)
                .taskType(TaskConstant.TASK_TYPE_EXAM_SCREENSHOTS)
                .status(TASK_STATUS_PENDING)
                .createdAt(currentTime)
                .updatedAt(currentTime)
                .build();
        mongoDBUtils.save(task);
        logger.info("Service: 已创建截图导出任务 {}，状态: PENDING", taskId);

        // 异步执行导出任务 - 使用独立的异步服务
        asyncExportTaskService.executeExportTaskAsync(taskId, examId, TaskConstant.TASK_TYPE_EXAM_SCREENSHOTS, exam, task);

        logger.info("Service: 截图导出任务 {} 已提交到异步服务，立即返回任务ID", taskId);
        return taskId;
    }



    @Override
    public ExportTaskStatusVO getExportTaskStatus(String taskId) {
        logger.debug("Service: 查询导出任务状态, 任务ID: {}", taskId);
        ExportTask task = mongoDBUtils.findById(taskId, ExportTask.class);

        Optional.ofNullable(task)
                .orElseThrow(() -> new EntityNotFoundException("导出任务不存在: " + taskId));

        return ExportTaskStatusVO.builder()
                .taskId(task.getId())
                .examId(task.getExamId())
                .taskType(task.getTaskType())
                .status(task.getStatus())
                .fileUrl(task.getFileUrl())
                .fileName(task.getFileName())
                .errorMessage(task.getErrorMessage())
                .createdAt(task.getCreatedAt())
                .updatedAt(task.getUpdatedAt())
                .build();
    }


    /**
     * 根据考场ID，生成用于Excel导出的数据列表。
     *
     * @param examId 考场ID
     * @return 转换并聚合后的DTO列表，可直接用于Excel导出
     */
    public List<ExamLogExportDTO> generateExportDataByExamId(String examId) {
        // --- 1. 性能优化：一次性查询所有相关学生信息，并存入Map ---
        // 根据examId从报名记录表中查出本次考试所有报名的学生
        List<CompetitionSignUpLog> signUps = mongoDBUtils.findByField("examID", examId, CompetitionSignUpLog.class);

        // 将学生列表转换为一个Map，Key为准考证号(examCard)，Value为报名记录对象
        // 这样后续可以 O(1) 的时间复杂度快速查找学生信息，避免N+1查询
        Map<String, CompetitionSignUpLog> signUpMap = signUps.stream()
                .collect(Collectors.toMap(CompetitionSignUpLog::getExamCard, Function.identity(), (existing, replacement) -> existing));
        logger.info("为考场ID '{}' 查询到 {} 条报名记录。", examId, signUpMap.size());

        // --- 2. 查询主数据：所有答题记录 ---
        // 根据examId查询所有学生的答题记录日志
        // 注意：CompetitionQuestionLog实体中字段为 `ExamId` (大写I)
        List<CompetitionQuestionLog> questionLogs = mongoDBUtils.findByField("ExamId", examId, CompetitionQuestionLog.class);
        logger.info("为考场ID '{}' 查询到 {} 条答题总记录。", examId, questionLogs.size());


        // --- 3. 核心逻辑：数据扁平化与聚合 ---
        final List<ExamLogExportDTO> exportDataList = new ArrayList<>();
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

        // 遍历每个学生的答题记录总log
        for (CompetitionQuestionLog log : questionLogs) {
            // 从Map中通过准考证号获取该学生的报名信息
            CompetitionSignUpLog studentInfo = signUpMap.get(log.getExamCard());
            if (studentInfo == null) {
                logger.warn("数据不一致：找到了准考证号为 {} 的答题记录，但未找到对应的报名信息。跳过此条记录。", log.getExamCard());
                continue; // 如果找不到报名信息，则跳过该学生的所有答题记录
            }

            // 如果该学生的studentAnswer列表为空，则跳过
            if (log.getStudentAnswer() == null || log.getStudentAnswer().isEmpty()) {
                continue;
            }

            // 遍历该学生的每一条答案（这正是“扁平化”操作）
            for (Answer answer : log.getStudentAnswer()) {
                var dto = new ExamLogExportDTO();

                // a. 从学生报名信息(studentInfo)中填充数据

                //区名比较特殊需要根据学校代码到关联的School中查询zoneName字段
                 School school = mongoDBUtils.findOne(Criteria.where("schoolCode").is(studentInfo.getSchoolCode()), School.class);
                 dto.setZoneName(school != null ? school.getZoneName() : "未知区");

                dto.setSchool(studentInfo.getSchool());
                dto.setClassName(studentInfo.getClassName());
                dto.setStudentName(studentInfo.getName());
                dto.setExamCard(studentInfo.getExamCard());

                // b. 从答案(Answer)对象中填充具体答题数据
                dto.setTaskName(answer.getTaskName());
                dto.setQuestionId(answer.getQuestionId()); // 题号类型已改为String
                dto.setOption(answer.getOption());

                // c. 数据格式化与转换
                // 使用JDK 17的Switch表达式，代码更简洁
                dto.setQuestionType(switch (Optional.ofNullable(answer.getQuestionType()).orElse(-1)) {
                    case 0 -> "主观题";
                    case 1 -> "客观题";
                    default -> "未知";
                });

                // 将毫秒时间戳转换为格式化日期字符串
                if (answer.getSbmitTime() != null) {
                    dto.setSubmitTime(sdf.format(new Date(answer.getSbmitTime())));
                }

                // 将文本答案Map拼接成一个字符串
                if (answer.getText() != null && !answer.getText().isEmpty()) {
                    String combinedText = answer.getText().entrySet().stream()
                            .map(entry -> entry.getKey() + ": " + entry.getValue())
                            .collect(Collectors.joining("\n")); // 使用换行符分隔，在Excel中会显示为多行
                    dto.setTextAnswer(combinedText);
                } else {
                    // 根据您的图片，有些行显示"文本答案"，这里进行模拟
                    // 如果是主观题且没有文本，可以显示一个提示
                    if (Objects.equals(answer.getQuestionType(), 0)) {
                        dto.setTextAnswer("该题无文本答案");
                    }
                }

                // 将URL字符串转换为URL对象，EasyExcel会自动处理
                try {
                    if (answer.getUrl() != null && !answer.getUrl().isBlank()) {
                        dto.setAnswerImageUrl(new URL(answer.getUrl()));
                    }
                } catch (MalformedURLException e) {
                    logger.error("发现无效的图片URL: {}", answer.getUrl(), e);
                    // URL无效，Excel中该单元格将为空
                }

                // 将完整填充的DTO添加到最终列表中
                exportDataList.add(dto);
            }
        }

        return exportDataList;
    }


}
