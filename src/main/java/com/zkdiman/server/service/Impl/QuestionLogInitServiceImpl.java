package com.zkdiman.server.service.Impl;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import com.zkdiman.server.reposity.QuestionReposity;
import com.zkdiman.server.service.QuestionLogInitService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 答题记录初始化服务实现
 * 负责为学生初始化答题记录表并绑定考场
 */
@Service
public class QuestionLogInitServiceImpl implements QuestionLogInitService {
    
    private static final Logger logger = LogManager.getLogger(QuestionLogInitServiceImpl.class);
    
    @Autowired
    private QuestionReposity questionReposity;
    
    @Autowired
    private MongoDBUtils mongoDBUtils;
    
    @Override
    public CompetitionQuestionLog initQuestionLogForStudent(CompetitionSignUpLog student) {
        if (student == null || !StringUtils.hasText(student.getExamCard())) {
            logger.warn("学生信息无效，无法初始化答题记录");
            return null;
        }
        
        logger.info("为学生 {} (准考证号: {}) 初始化答题记录，考试ID: {}", 
                   student.getName(), student.getExamCard(), student.getExamID());
        
        try {
            // 检查是否已存在答题记录
            CompetitionQuestionLog existingLog = questionReposity.findByExamCard(student.getExamCard());
            
            if (existingLog != null) {
                // 如果已存在，更新考试ID（考场绑定）
                if (!student.getExamID().equals(existingLog.getExamId())) {
                    existingLog.setExamId(student.getExamID());
                    CompetitionQuestionLog savedLog = mongoDBUtils.save(existingLog);
                    logger.info("更新学生 {} 的答题记录考试ID: {} -> {}", 
                               student.getExamCard(), existingLog.getExamId(), student.getExamID());
                    return savedLog;
                } else {
                    logger.debug("学生 {} 的答题记录已存在且考试ID正确，无需更新", student.getExamCard());
                    return existingLog;
                }
            } else {
                // 创建新的答题记录
                CompetitionQuestionLog newLog = new CompetitionQuestionLog();
                newLog.setExamCard(student.getExamCard());
                newLog.setExamId(student.getExamID());
                newLog.setStudentAnswer(new ArrayList<>()); // 初始化空的答题记录
                newLog.setTaskList(new ArrayList<>());      // 初始化空的任务列表
                
                CompetitionQuestionLog savedLog = mongoDBUtils.save(newLog);
                if (savedLog != null) {
                    logger.info("为学生 {} 创建新的答题记录成功，考试ID: {}", 
                               student.getExamCard(), student.getExamID());
                    return savedLog;
                } else {
                    logger.error("为学生 {} 创建答题记录失败", student.getExamCard());
                    return null;
                }
            }
        } catch (Exception e) {
            logger.error("为学生 {} 初始化答题记录时发生异常: {}", student.getExamCard(), e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public int batchInitQuestionLogsForStudents(List<CompetitionSignUpLog> students) {
        if (students == null || students.isEmpty()) {
            logger.info("学生列表为空，无需初始化答题记录");
            return 0;
        }
        
        logger.info("开始批量初始化 {} 个学生的答题记录", students.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (CompetitionSignUpLog student : students) {
            try {
                CompetitionQuestionLog result = initQuestionLogForStudent(student);
                if (result != null) {
                    successCount++;
                } else {
                    failureCount++;
                    logger.warn("学生 {} 答题记录初始化失败", student.getExamCard());
                }
            } catch (Exception e) {
                failureCount++;
                logger.error("学生 {} 答题记录初始化异常: {}", student.getExamCard(), e.getMessage(), e);
            }
        }
        
        logger.info("批量初始化答题记录完成: 成功 {} 个，失败 {} 个", successCount, failureCount);
        return successCount;
    }
    
    @Override
    public boolean hasQuestionLog(String examCard, String examId) {
        if (!StringUtils.hasText(examCard)) {
            return false;
        }
        
        try {
            Criteria criteria = Criteria.where("examCard").is(examCard);
            if (StringUtils.hasText(examId)) {
                criteria.and("ExamId").is(examId);
            }
            
            CompetitionQuestionLog log = mongoDBUtils.findOne(criteria, CompetitionQuestionLog.class);
            return log != null;
        } catch (Exception e) {
            logger.error("检查学生 {} 答题记录时发生异常: {}", examCard, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public CompetitionQuestionLog createOrUpdateQuestionLog(CompetitionSignUpLog student) {
        if (student == null || !StringUtils.hasText(student.getExamCard()) || !StringUtils.hasText(student.getExamID())) {
            logger.warn("学生信息不完整，无法创建或更新答题记录: examCard={}, examID={}", 
                       student != null ? student.getExamCard() : "null",
                       student != null ? student.getExamID() : "null");
            return null;
        }
        
        try {
            // 先按准考证号查找现有记录
            CompetitionQuestionLog existingLog = questionReposity.findByExamCard(student.getExamCard());
            
            if (existingLog != null) {
                // 更新现有记录的考试ID
                boolean needUpdate = false;
                
                if (!student.getExamID().equals(existingLog.getExamId())) {
                    existingLog.setExamId(student.getExamID());
                    needUpdate = true;
                    logger.info("更新学生 {} 的答题记录考试ID: {}", student.getExamCard(), student.getExamID());
                }
                
                // 确保基础字段不为空
                if (existingLog.getStudentAnswer() == null) {
                    existingLog.setStudentAnswer(new ArrayList<>());
                    needUpdate = true;
                }
                
                if (existingLog.getTaskList() == null) {
                    existingLog.setTaskList(new ArrayList<>());
                    needUpdate = true;
                }
                
                if (needUpdate) {
                    return mongoDBUtils.save(existingLog);
                } else {
                    logger.debug("学生 {} 的答题记录无需更新", student.getExamCard());
                    return existingLog;
                }
            } else {
                // 创建新记录
                CompetitionQuestionLog newLog = new CompetitionQuestionLog();
                newLog.setExamCard(student.getExamCard());
                newLog.setExamId(student.getExamID());
                newLog.setStudentAnswer(new ArrayList<>());
                newLog.setTaskList(new ArrayList<>());
                
                CompetitionQuestionLog savedLog = mongoDBUtils.save(newLog);
                logger.info("为学生 {} 创建新的答题记录，考试ID: {}", student.getExamCard(), student.getExamID());
                return savedLog;
            }
        } catch (Exception e) {
            logger.error("为学生 {} 创建或更新答题记录时发生异常: {}", student.getExamCard(), e.getMessage(), e);
            return null;
        }
    }
}
