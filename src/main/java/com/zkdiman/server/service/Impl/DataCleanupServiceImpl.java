package com.zkdiman.server.service.Impl;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import com.zkdiman.server.service.AnswerSubmissionService;
import com.zkdiman.server.service.DataCleanupService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据清理服务实现
 * 负责清理系统中的重复和无效数据
 */
@Service
public class DataCleanupServiceImpl implements DataCleanupService {
    
    private static final Logger logger = LogManager.getLogger(DataCleanupServiceImpl.class);
    
    @Autowired
    private MongoDBUtils mongoDBUtils;
    
    @Autowired
    private AnswerSubmissionService answerSubmissionService;
    
    @Override
    public CleanupResult cleanupDuplicateAnswers() {
        logger.info("开始清理所有学生的重复答题记录...");
        
        // 查询所有答题记录
        List<CompetitionQuestionLog> allQuestionLogs = mongoDBUtils.findAll(CompetitionQuestionLog.class);
        
        int totalStudents = allQuestionLogs.size();
        int studentsWithDuplicates = 0;
        int totalDuplicatesRemoved = 0;
        int totalRecordsAfter = 0;
        
        for (CompetitionQuestionLog questionLog : allQuestionLogs) {
            if (questionLog.getStudentAnswer() == null || questionLog.getStudentAnswer().isEmpty()) {
                continue;
            }
            
            int originalSize = questionLog.getStudentAnswer().size();
            ArrayList<Answer> cleanedAnswers = removeDuplicateAnswers(questionLog.getStudentAnswer());
            int cleanedSize = cleanedAnswers.size();
            
            if (originalSize > cleanedSize) {
                studentsWithDuplicates++;
                int duplicatesRemoved = originalSize - cleanedSize;
                totalDuplicatesRemoved += duplicatesRemoved;
                
                // 更新数据库
                questionLog.setStudentAnswer(cleanedAnswers);
                mongoDBUtils.save(questionLog);
                
                logger.info("学生 {} 清理了 {} 个重复答题记录 ({} -> {})", 
                           questionLog.getExamCard(), duplicatesRemoved, originalSize, cleanedSize);
            }
            
            totalRecordsAfter += cleanedSize;
        }
        
        CleanupResult result = new CleanupResult(totalStudents, studentsWithDuplicates, totalDuplicatesRemoved, totalRecordsAfter);
        logger.info("重复答题记录清理完成: {}", result);
        
        return result;
    }
    
    @Override
    public CleanupResult cleanupDuplicateAnswersForStudent(String examCard) {
        logger.info("开始清理学生 {} 的重复答题记录...", examCard);
        
        CompetitionQuestionLog questionLog = mongoDBUtils.findOne(
            Criteria.where("examCard").is(examCard), 
            CompetitionQuestionLog.class
        );
        
        if (questionLog == null) {
            logger.info("学生 {} 没有答题记录", examCard);
            return new CleanupResult(0, 0, 0, 0);
        }
        
        if (questionLog.getStudentAnswer() == null || questionLog.getStudentAnswer().isEmpty()) {
            logger.info("学生 {} 的答题记录为空", examCard);
            return new CleanupResult(1, 0, 0, 0);
        }
        
        int originalSize = questionLog.getStudentAnswer().size();
        ArrayList<Answer> cleanedAnswers = removeDuplicateAnswers(questionLog.getStudentAnswer());
        int cleanedSize = cleanedAnswers.size();
        
        int duplicatesRemoved = originalSize - cleanedSize;
        int studentsWithDuplicates = duplicatesRemoved > 0 ? 1 : 0;
        
        if (duplicatesRemoved > 0) {
            // 更新数据库
            questionLog.setStudentAnswer(cleanedAnswers);
            mongoDBUtils.save(questionLog);
            
            logger.info("学生 {} 清理了 {} 个重复答题记录 ({} -> {})", 
                       examCard, duplicatesRemoved, originalSize, cleanedSize);
        } else {
            logger.info("学生 {} 没有重复的答题记录", examCard);
        }
        
        CleanupResult result = new CleanupResult(1, studentsWithDuplicates, duplicatesRemoved, cleanedSize);
        logger.info("学生 {} 重复答题记录清理完成: {}", examCard, result);
        
        return result;
    }
    
    @Override
    public ValidationResult validateAnswerIntegrity() {
        logger.info("开始验证答题记录完整性...");
        
        List<CompetitionQuestionLog> allQuestionLogs = mongoDBUtils.findAll(CompetitionQuestionLog.class);
        
        int totalStudents = allQuestionLogs.size();
        int studentsWithIssues = 0;
        int totalAnswers = 0;
        int invalidAnswers = 0;
        
        for (CompetitionQuestionLog questionLog : allQuestionLogs) {
            boolean hasIssues = false;
            
            if (questionLog.getStudentAnswer() != null) {
                for (Answer answer : questionLog.getStudentAnswer()) {
                    totalAnswers++;
                    
                    if (!answerSubmissionService.validateAnswer(answer)) {
                        invalidAnswers++;
                        hasIssues = true;
                        logger.warn("学生 {} 的答题记录无效: questionId={}, questionType={}", 
                                   questionLog.getExamCard(), answer.getQuestionId(), answer.getQuestionType());
                    }
                }
            }
            
            if (hasIssues) {
                studentsWithIssues++;
            }
        }
        
        ValidationResult result = new ValidationResult(totalStudents, studentsWithIssues, totalAnswers, invalidAnswers);
        logger.info("答题记录完整性验证完成: {}", result);
        
        return result;
    }
    
    /**
     * 移除重复的答题记录
     * 对于相同的questionId和questionType组合，只保留最新的记录（根据sbmitTime）
     * 
     * @param answers 原始答题记录列表
     * @return 去重后的答题记录列表
     */
    private ArrayList<Answer> removeDuplicateAnswers(ArrayList<Answer> answers) {
        if (answers == null || answers.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用Map来存储每个题目的最新答案
        Map<String, Answer> latestAnswers = new HashMap<>();
        
        for (Answer answer : answers) {
            if (!answerSubmissionService.validateAnswer(answer)) {
                logger.warn("跳过无效的答题记录: questionId={}, questionType={}", 
                           answer.getQuestionId(), answer.getQuestionType());
                continue;
            }
            
            String key = answerSubmissionService.getAnswerKey(answer);
            Answer existingAnswer = latestAnswers.get(key);
            
            if (existingAnswer == null || answer.getSbmitTime() > existingAnswer.getSbmitTime()) {
                // 如果没有现有答案，或者当前答案更新，则保留当前答案
                latestAnswers.put(key, answer);
            }
        }
        
        // 按照原始顺序返回去重后的答案（根据第一次出现的顺序）
        ArrayList<Answer> result = new ArrayList<>();
        Set<String> addedKeys = new HashSet<>();
        
        for (Answer answer : answers) {
            if (!answerSubmissionService.validateAnswer(answer)) {
                continue;
            }
            
            String key = answerSubmissionService.getAnswerKey(answer);
            if (!addedKeys.contains(key)) {
                result.add(latestAnswers.get(key));
                addedKeys.add(key);
            }
        }
        
        return result;
    }
}
