package com.zkdiman.server.service.Impl;

import com.zkdiman.common.exception.EntityNotFoundException;
import com.zkdiman.common.exception.BusinessException;
import com.zkdiman.common.result.PageResult;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.exam.ExamAddDTO;
import com.zkdiman.pojo.dto.exam.ExamQueryDTO;
import com.zkdiman.pojo.dto.exam.ExamUpdateDTO;
import com.zkdiman.pojo.dto.exam.ExamStudentQueryDTO;
import com.zkdiman.pojo.dto.exam.ExamAddStudentsDTO;
import com.zkdiman.pojo.dto.student.StudentTimeAdjustmentDTO;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import com.zkdiman.pojo.entity.School;
import com.zkdiman.server.service.ExamService;
import com.zkdiman.server.service.QuestionLogInitService;
import com.zkdiman.server.service.strategy.studentimport.StudentImportStrategyManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import org.springframework.web.multipart.MultipartFile;
import static com.zkdiman.common.utils.ExcelUtils.parseExcelFile;
import com.zkdiman.server.service.strategy.exam.UpdateExamStrategy;
import com.zkdiman.server.service.strategy.studentexamtime.StudentExamTimeAdjustmentStrategy;


@Service
public class ExamServiceImpl implements ExamService {

    private static final Logger logger = LogManager.getLogger(ExamServiceImpl.class);

    @Autowired
    private MongoDBUtils mongoDBUtils;

    private final List<UpdateExamStrategy> updateExamStrategies;
    private final List<StudentExamTimeAdjustmentStrategy> studentExamTimeAdjustmentStrategies;
    private final StudentImportStrategyManager studentImportStrategyManager;

    @Autowired
    private QuestionLogInitService questionLogInitService;

    @Autowired
    public ExamServiceImpl(MongoDBUtils mongoDBUtils, 
                          List<UpdateExamStrategy> updateExamStrategies,
                          List<StudentExamTimeAdjustmentStrategy> studentExamTimeAdjustmentStrategies,
                          StudentImportStrategyManager studentImportStrategyManager) {
        this.mongoDBUtils = mongoDBUtils;
        this.updateExamStrategies = updateExamStrategies;
        this.studentExamTimeAdjustmentStrategies = studentExamTimeAdjustmentStrategies;
        this.studentImportStrategyManager = studentImportStrategyManager;
    }

    @Override
    public PageResult getExamListPage(ExamQueryDTO examQueryDTO) {
        Criteria criteria = new Criteria();

        if (org.springframework.util.StringUtils.hasText(examQueryDTO.getExamName())) {
            criteria.and("examName").regex(".*" + examQueryDTO.getExamName() + ".*", "i");
        }

        if (examQueryDTO.getStatus() != null) {
            criteria.and("status").is(examQueryDTO.getStatus());
        }

        int page = examQueryDTO.getPage() - 1;
        int pageSize = examQueryDTO.getPageSize();

        Page<Exam> examPage = mongoDBUtils.findPage(
                criteria,
                page,
                pageSize,
                "createTime",
                Sort.Direction.DESC,
                Exam.class
        );

        return new PageResult(examPage.getTotalElements(), new ArrayList<>(examPage.getContent()));
    }

    @Override
    public void addExam(ExamAddDTO examAddDTO) {
        logger.info("Service: 新增考试场次，参数: {}", examAddDTO);
        Exam exam = new Exam();
        BeanUtils.copyProperties(examAddDTO, exam);

        if (exam.getStatus() == null) {
            exam.setStatus(0);
        }
        long currentTimeMillis = System.currentTimeMillis();
        exam.setCreateTime(currentTimeMillis);
        exam.setUpdateTime(currentTimeMillis);
        if (exam.getStudentCount() == null) {
             exam.setStudentCount(0);
        }

        mongoDBUtils.save(exam);
        logger.info("Service: 考试场次新增成功, ID: {}", exam.getID());
    }

    @Override
    public Exam getExamById(String id) {
        logger.info("Service: 根据ID查询考试场次, ID: {}", id);
        Exam exam = mongoDBUtils.findById(id, Exam.class);
        if (exam == null) {
            throw new EntityNotFoundException("考试场次不存在");
        }
        return exam;
    }

    @Override
    public void updateExam(String id, ExamUpdateDTO examUpdateDTO) {
        logger.info("Service: 修改考试场次, ID: {}, 数据: {}", id, examUpdateDTO);

        Exam existingExam = mongoDBUtils.findById(id, Exam.class);
        if (existingExam == null) {
            throw new EntityNotFoundException("考试场次不存在: " + id);
        }

        Map<String, Object> examUpdateMap = new HashMap<>();
        Map<String, Object> studentUpdateMap = new HashMap<>();

        // 遍历所有策略并应用更新
        for (UpdateExamStrategy strategy : updateExamStrategies) {
            strategy.applyUpdate(existingExam, examUpdateDTO, examUpdateMap, studentUpdateMap);
        }

        if (!examUpdateMap.isEmpty()) {
            examUpdateMap.put("updateTime", System.currentTimeMillis());
            boolean examUpdated = mongoDBUtils.updateOne(Criteria.where("ID").is(id), examUpdateMap, Exam.class);
            if (!examUpdated) {
                logger.warn("Service: 更新考试场次失败 or no changes made, ID: {}", id);
            } else {
                logger.info("Service: 考试场次基础信息更新已提交, ID: {}", id);
            }
        } else {
            logger.info("Service: 没有可更新的考试场次字段, ID: {}", id);
            // 如果 examUpdateMap 为空，但 studentUpdateMap 不为空，则不需要返回
            // 即使考试本身没有变化，关联的学生信息可能仍需更新
        }

        if (!studentUpdateMap.isEmpty()) {
            Criteria studentCriteria = Criteria.where("examID").is(id);
            boolean studentsUpdated = mongoDBUtils.updateMany(studentCriteria, studentUpdateMap, CompetitionSignUpLog.class);
            if (studentsUpdated) {
                logger.info("Service: 成功级联更新考试场次 ID {} 下学员的相关信息.", id);
            } else {
                logger.warn("Service: 级联更新考试场次 ID {} 下学员信息的操作未被确认或无匹配学员.", id);
            }
        }
    }

    @Override
    public void deleteExamById(String id) {
        logger.info("Service: 删除考试场次, ID: {}", id);
        boolean deleted = mongoDBUtils.deleteOne(Criteria.where("ID").is(id), Exam.class);
        if (!deleted) {
            logger.warn("Service: 删除考试场次失败, ID: {}", id);
            throw new com.zkdiman.common.exception.EntityNotFoundException("考试场次不存在或已删除");
        }
        logger.info("Service: 考试场次删除成功, ID: {}", id);
    }

    @Override
    public PageResult getStudentsByExamId(String examId, ExamStudentQueryDTO queryDTO) {
        logger.info("Service: 查询考试场次 {} 下的学员列表, 参数: {}", examId, queryDTO);

        Criteria criteria = Criteria.where("examID").is(examId);

        if (StringUtils.hasText(queryDTO.getExamCard())) {
            criteria.and("examCard").is(queryDTO.getExamCard());
        }
        if (StringUtils.hasText(queryDTO.getName())) {
            criteria.and("name").regex(queryDTO.getName(), "i");
        }
        if (StringUtils.hasText(queryDTO.getSchool())) {
            criteria.and("school").regex(queryDTO.getSchool(), "i");
        }

        Page<CompetitionSignUpLog> studentPage = mongoDBUtils.findPage(
                criteria,
                queryDTO.getPage() - 1,
                queryDTO.getPageSize(),
                "examCard",
                Sort.Direction.ASC,
                CompetitionSignUpLog.class
        );

        return new PageResult(studentPage.getTotalElements(), new ArrayList<>(studentPage.getContent()));
    }

    @Override
    public void addStudentsToExam(String examId, ExamAddStudentsDTO dto) {
        if (dto.getStudentIds() == null || dto.getStudentIds().isEmpty()) {
            logger.info("Service: 学员ID列表为空，无需为考试场次 {} 添加学员", examId);
            return;
        }
        logger.info("Service: 为考试场次 {} 批量添加学员, DTO: {}", examId, dto);

        Exam exam = mongoDBUtils.findById(examId, Exam.class);
        if (exam == null) {
            throw new EntityNotFoundException("考试场次不存在: " + examId);
        }

        Criteria criteria = Criteria.where("ID").in(dto.getStudentIds());
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("examID", examId);
        updateMap.put("startTime", dto.getStartTime());
        updateMap.put("duration", dto.getDuration());

        if (StringUtils.hasText(dto.getExamRoom())) {
            updateMap.put("examRoom", dto.getExamRoom());
        }
        if (StringUtils.hasText(dto.getSeatNumber())) {
            updateMap.put("seatNumber", dto.getSeatNumber());
        }

        boolean updated = mongoDBUtils.updateMany(criteria, updateMap, CompetitionSignUpLog.class);

        if (!updated) {
            logger.warn("Service: 为考试场次 {} 添加学员的操作可能未完全成功或未被确认", examId);
        } else {
            // 为添加到考试场次的学员初始化答题记录
            try {
                // 查询刚刚更新的学员信息
                List<CompetitionSignUpLog> updatedStudents = mongoDBUtils.find(
                    Criteria.where("ID").in(dto.getStudentIds()).and("examID").is(examId),
                    CompetitionSignUpLog.class
                );

                if (!updatedStudents.isEmpty()) {
                    logger.info("为考试场次 {} 中的 {} 个学员初始化答题记录", examId, updatedStudents.size());
                    int initSuccessCount = questionLogInitService.batchInitQuestionLogsForStudents(updatedStudents);
                    logger.info("考试场次 {} 学员答题记录初始化完成: {} / {} 成功",
                               examId, initSuccessCount, updatedStudents.size());
                } else {
                    logger.warn("未找到考试场次 {} 中的已更新学员，跳过答题记录初始化", examId);
                }
            } catch (Exception e) {
                logger.error("为考试场次 {} 的学员初始化答题记录时发生异常: {}", examId, e.getMessage(), e);
                // 不抛出异常，避免影响学员添加的成功状态
            }
        }

        logger.info("Service: 为考试场次 {} 添加学员的操作已提交", examId);
    }

    @Override
    public void removeStudentsFromExam(String examId, List<String> studentIds) {
        if (studentIds == null || studentIds.isEmpty()) {
            logger.info("Service: 学员ID列表为空，无需从考试场次 {} 移除学员", examId);
            return;
        }
        logger.info("Service: 从考试场次 {} 批量移除学员, 学员IDs: {}", examId, studentIds);

        Criteria criteria = Criteria.where("ID").in(studentIds).and("examID").is(examId);

        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("examID", null); 

        boolean updated = mongoDBUtils.updateMany(criteria, updateMap, CompetitionSignUpLog.class);

        if (!updated) {
            logger.warn("Service: 从考试场次 {} 移除学员的操作可能未完全成功或未被确认", examId);
        }
        logger.info("Service: 从考试场次 {} 移除学员的操作已提交", examId);
    }

    @Override
    public void adjustStudentExamTime(String examId, String studentId, StudentTimeAdjustmentDTO studentTimeAdjustmentDTO) {
        logger.info("Adjusting exam time for student {} in exam {}, adjustment: {}", studentId, examId, studentTimeAdjustmentDTO);

        Exam exam = mongoDBUtils.findById(examId, Exam.class);
        if (exam == null) {
            throw new BusinessException("考试场次不存在: " + examId);
        }

        Criteria logCriteria = Criteria.where("_id").is(studentId).and("examID").is(examId);
        CompetitionSignUpLog signUpLog = mongoDBUtils.findOne(logCriteria, CompetitionSignUpLog.class);
        if (signUpLog == null) {
            throw new BusinessException("指定的学生报名记录不存在或不属于该考试场次");
        }

        Map<String, Object> updateMap = new HashMap<>();

        for (StudentExamTimeAdjustmentStrategy strategy : studentExamTimeAdjustmentStrategies) {
            strategy.applyAdjustment(exam, signUpLog, studentTimeAdjustmentDTO, updateMap, logger);
        }

        if (updateMap.isEmpty()) {
            // 如果没有任何策略填充 updateMap，则意味着 DTO 中没有提供有效的 startTime 或 duration
            // 或者提供的所有值都未通过策略的验证逻辑（尽管当前策略设计中，无效值会抛异常）
            // 之前的逻辑是如果 updateMap 为空，则抛出 "未提供任何调整信息"
            // 我们在这里保留这个逻辑，因为如果DTO为空，策略也不会向map添加任何内容
            throw new BusinessException("未提供任何有效的调整信息（开始时间或时长）或调整未通过验证");
        }

        boolean updated = mongoDBUtils.updateOne(logCriteria, updateMap, CompetitionSignUpLog.class);
        if (!updated) {
            logger.error("Failed to update student exam time for studentId: {} in examId: {}", studentId, examId);
            throw new BusinessException("调整学生考试时间失败，请稍后重试或联系管理员");
        }

        logger.info("Successfully adjusted exam time for student {} in exam {}", studentId, examId);
    }

    @Override
    public BatchImportResultVO batchImportAssignedStudents(String examId, MultipartFile file, Long requestStartTime, Integer requestDuration) {
        logger.info("Attempting batch import for examId: {}, file: {}, startTime: {}, duration: {}", 
                  examId, file.getOriginalFilename(), requestStartTime, requestDuration);

        Exam exam = mongoDBUtils.findById(examId, Exam.class);
        if (exam == null) {
            throw new BusinessException("考试场次不存在: " + examId);
        }

        List<BatchImportResultVO.ImportError> errors = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        List<StudentExcelDataDTO> studentDataList = parseExcelFile(file, errors);

        for (StudentExcelDataDTO studentData : studentDataList) {
            try {
                // 验证必填字段
                if (!StringUtils.hasText(studentData.getExamCard()) ||
                    !StringUtils.hasText(studentData.getName()) ||
                    !StringUtils.hasText(studentData.getSchool())) {
                    errors.add(BatchImportResultVO.ImportError.builder()
                        .rowNumber(studentData.getExcelRowNumber())
                        .identifier(studentData.getExamCard() != null ? studentData.getExamCard() : "N/A")
                        .reason("准考证号、姓名、学校为必填项")
                        .build());
                    failureCount++;
                    continue;
                }

                // 处理学生记录
                CompetitionSignUpLog signUpLog = mongoDBUtils.findOne(
                    Criteria.where("examCard").is(studentData.getExamCard()), 
                    CompetitionSignUpLog.class
                );
                
                boolean isNewStudent = (signUpLog == null);
                if (isNewStudent) {
                    signUpLog = new CompetitionSignUpLog();
                    signUpLog.setExamCard(studentData.getExamCard());
                } else {
                    if (examId.equals(signUpLog.getExamID())) {
                        logger.warn("Student {} (ExamCard: {}) is already in exam {}. Updating details.", 
                                  signUpLog.getName(), signUpLog.getExamCard(), examId);
                    } else if (signUpLog.getExamID() != null) {
                        logger.warn("Student {} (ExamCard: {}) was in exam {}. Re-assigning to exam {}.", 
                                  signUpLog.getName(), signUpLog.getExamCard(), signUpLog.getExamID(), examId);
                    }
                }
                
                // 设置考试ID、开始时间和时长（由方法参数提供，不依赖Excel中的数据）
                signUpLog.setExamID(examId);
                signUpLog.setStartTime(requestStartTime);
                signUpLog.setDuration(requestDuration);
                
                // 使用策略模式处理所有数据字段，避免大量if语句
                boolean processSuccess = studentImportStrategyManager.executeStrategies(
                    studentData, signUpLog, mongoDBUtils, logger
                );
                
                // 保存学生记录
                CompetitionSignUpLog savedStudent = mongoDBUtils.save(signUpLog);
                if (savedStudent != null) {
                    successCount++;

                    // 为学生初始化答题记录
                    try {
                        CompetitionQuestionLog questionLog = questionLogInitService.createOrUpdateQuestionLog(savedStudent);
                        if (questionLog != null) {
                            logger.debug("为学生 {} 初始化答题记录成功，考试ID: {}",
                                       savedStudent.getExamCard(), examId);
                        } else {
                            logger.warn("为学生 {} 初始化答题记录失败", savedStudent.getExamCard());
                        }
                    } catch (Exception e) {
                        logger.error("为学生 {} 初始化答题记录时发生异常: {}",
                                   savedStudent.getExamCard(), e.getMessage(), e);
                        // 不影响学生导入的成功状态
                    }

                    if (!processSuccess) {
                        logger.warn("Some import strategies failed for student: {}, but record was still saved",
                                  studentData.getExamCard());
                    }
                } else {
                    logger.error("保存学生记录失败: {}", studentData.getExamCard());
                    errors.add(BatchImportResultVO.ImportError.builder()
                        .rowNumber(studentData.getExcelRowNumber())
                        .identifier(studentData.getExamCard())
                        .reason("保存学生记录失败")
                        .build());
                    failureCount++;
                }

            } catch (Exception e) {
                logger.error("Failed to process student data from Excel row {}: {}", 
                           studentData.getExcelRowNumber(), e.getMessage(), e);
                errors.add(BatchImportResultVO.ImportError.builder()
                    .rowNumber(studentData.getExcelRowNumber())
                    .identifier(studentData.getExamCard() != null ? studentData.getExamCard() : "N/A")
                    .reason("处理数据时发生内部错误: " + e.getMessage())
                    .build());
                failureCount++;
            }
        }
        
        // 更新考试场次的学生数量
        if (successCount > 0 && exam.getStudentCount() != null) {
            Map<String, Object> examUpdateMap = new HashMap<>();
            long currentStudentCountInExam = mongoDBUtils.count(Criteria.where("examID").is(examId), CompetitionSignUpLog.class);
            examUpdateMap.put("studentCount", (int) currentStudentCountInExam);
            mongoDBUtils.updateOne(Criteria.where("ID").is(examId), examUpdateMap, Exam.class);
        }

        return BatchImportResultVO.builder()
            .successCount(successCount)
            .failureCount(failureCount)
            .errors(errors)
            .build();
    }
} 