package com.zkdiman.server.service.Impl;

import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.server.service.AnswerSubmissionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;

/**
 * 答题记录提交服务实现
 * 负责处理学生答题记录的提交和更新逻辑
 */
@Service
public class AnswerSubmissionServiceImpl implements AnswerSubmissionService {
    
    private static final Logger logger = LogManager.getLogger(AnswerSubmissionServiceImpl.class);
    
    @Override
    public ArrayList<Answer> addOrUpdateAnswer(ArrayList<Answer> studentAnswers, Answer newAnswer, String examCard) {
        if (studentAnswers == null) {
            studentAnswers = new ArrayList<>();
        }
        
        if (!validateAnswer(newAnswer)) {
            logger.warn("学生 {} 提交的答题记录无效: questionId={}, questionType={}", 
                       examCard, newAnswer.getQuestionId(), newAnswer.getQuestionType());
            return studentAnswers;
        }
        
        // 查找是否已存在相同的题目答案
        int existingIndex = findExistingAnswerIndex(studentAnswers, newAnswer.getQuestionId(), newAnswer.getQuestionType());
        
        if (existingIndex >= 0) {
            // 找到相同的题目，覆盖现有答案
            Answer oldAnswer = studentAnswers.get(existingIndex);
            studentAnswers.set(existingIndex, newAnswer);
            
            logger.info("学生 {} 覆盖了题目 {} (类型: {}) 的答案", examCard, newAnswer.getQuestionId(), newAnswer.getQuestionType());
            logger.debug("学生 {} 题目 {} 答案更新: {} -> {}", 
                        examCard, newAnswer.getQuestionId(), oldAnswer.getUrl(), newAnswer.getUrl());
        } else {
            // 如果没有找到相同的题目，添加新答案
            studentAnswers.add(newAnswer);
            logger.info("学生 {} 新增了题目 {} (类型: {}) 的答案", 
                       examCard, newAnswer.getQuestionId(), newAnswer.getQuestionType());
        }
        
        return studentAnswers;
    }
    
    @Override
    public int findExistingAnswerIndex(ArrayList<Answer> studentAnswers, String questionId, Integer questionType) {
        if (studentAnswers == null || studentAnswers.isEmpty()) {
            return -1;
        }
        
        for (int i = 0; i < studentAnswers.size(); i++) {
            Answer existingAnswer = studentAnswers.get(i);
            if (isSameQuestion(existingAnswer, questionId, questionType)) {
                return i;
            }
        }
        
        return -1;
    }
    
    @Override
    public boolean validateAnswer(Answer answer) {
        if (answer == null) {
            return false;
        }
        
        // 检查必填字段
        if (!StringUtils.hasText(answer.getQuestionId())) {
            logger.warn("答题记录验证失败: questionId为空");
            return false;
        }
        
        if (answer.getQuestionType() == null) {
            logger.warn("答题记录验证失败: questionType为空");
            return false;
        }
        
        if (!StringUtils.hasText(answer.getUrl())) {
            logger.warn("答题记录验证失败: url为空");
            return false;
        }
        
        if (answer.getSbmitTime() == null) {
            logger.warn("答题记录验证失败: sbmitTime为空");
            return false;
        }
        
        // 检查题目类型是否有效（0-主观题，1-客观题）
        if (answer.getQuestionType() < 0 || answer.getQuestionType() > 1) {
            logger.warn("答题记录验证失败: questionType无效 ({})", answer.getQuestionType());
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getAnswerKey(Answer answer) {
        if (answer == null) {
            return null;
        }
        return answer.getQuestionId() + "_" + answer.getQuestionType();
    }
    
    /**
     * 判断两个答题记录是否为同一题目
     * 
     * @param answer 答题记录
     * @param questionId 题目ID
     * @param questionType 题目类型
     * @return 是否为同一题目
     */
    private boolean isSameQuestion(Answer answer, String questionId, Integer questionType) {
        if (answer == null || questionId == null || questionType == null) {
            return false;
        }
        
        return questionId.equals(answer.getQuestionId()) && 
               questionType.equals(answer.getQuestionType());
    }
}
