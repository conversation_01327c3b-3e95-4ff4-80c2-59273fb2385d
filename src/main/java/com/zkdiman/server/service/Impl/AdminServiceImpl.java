package com.zkdiman.server.service.Impl;

import com.zkdiman.common.exception.AdminException;
import com.zkdiman.common.exception.DeleteException;
import com.zkdiman.common.exception.EntityNotFoundException;
import com.zkdiman.common.result.PageResult;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.admin.AdminQueryDTO;
import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.entity.Admin;
import com.zkdiman.pojo.vo.admin.AdminLoginVO;
import com.zkdiman.server.reposity.AdminReposity;
import com.zkdiman.server.service.AdminService;
import com.zkdiman.common.exception.DuplicateKeyException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import com.zkdiman.server.service.strategy.admin.UpdateAdminStrategy;

@Service
public class AdminServiceImpl implements AdminService {

    private static final Logger logger = LogManager.getLogger(AdminServiceImpl.class);

    private static final String SYSTEM_ADMIN_ID = "6819c530c8aa7aca240390d1"; // 系统管理员ID (后续可更改)
    private static final String SYSTEM_ADMIN_USERNAME = "admin"; // 系统管理员用户名

    private final AdminReposity adminReposity;
    private final MongoDBUtils mongoDBUtils;
    private final List<UpdateAdminStrategy> updateAdminStrategies;

    @Autowired
    public AdminServiceImpl(AdminReposity adminReposity, MongoDBUtils mongoDBUtils, List<UpdateAdminStrategy> updateAdminStrategies) {
        this.adminReposity = adminReposity;
        this.mongoDBUtils = mongoDBUtils;
        this.updateAdminStrategies = updateAdminStrategies;

        // 添加调试日志，确认策略注入情况
        logger.info("AdminServiceImpl 初始化完成，注入了 {} 个更新策略", updateAdminStrategies.size());
        for (UpdateAdminStrategy strategy : updateAdminStrategies) {
            logger.debug("注入的策略: {}", strategy.getClass().getSimpleName());
        }
    }

    /**
     * 登录接口
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    @Override
    public AdminLoginVO login(String username, String password) {
        Admin admin = adminReposity.findByUsernameAndPassword(username, password);
        //如果为空直接报错
        if (admin == null) {
            throw new AdminException("用户名或密码错误");
        }
        // 确保只有启用的管理员可以登录
        if (admin.getEnabled() == null || !admin.getEnabled()) {
            throw new AdminException("该管理员账户已被禁用");
        }
        AdminLoginVO adminLoginVo = AdminLoginVO.builder()
                .username(admin.getUsername())
                .name(admin.getName())
                .ID(admin.getID())
                .build();
        return adminLoginVo;
    }

    @Override
    public PageResult getAdminListPage(AdminQueryDTO queryDTO) {
        logger.info("Service: 查询管理员列表, 参数: {}", queryDTO);
        Criteria criteria = new Criteria();
        if (StringUtils.hasText(queryDTO.getUsername())) {
            // 对于用户名，如果这是常见用例，使用精确匹配可能更好，
            // 或者如果打算进行部分匹配，则使用正则表达式。
            // 目前，为了与 'name' 字段保持一致，使用正则表达式。
            criteria.and("username").regex(queryDTO.getUsername(), "i"); 
        }
        if (StringUtils.hasText(queryDTO.getName())) {
            criteria.and("name").regex(queryDTO.getName(), "i");
        }
        if (queryDTO.getEnabled() != null) {
            criteria.and("enabled").is(queryDTO.getEnabled());
        }

        Page<Admin> adminPage = mongoDBUtils.findPage(
                criteria,
                queryDTO.getPage() - 1, // 页码是从0开始的
                queryDTO.getPageSize(),
                "username", // 默认排序字段
                Sort.Direction.ASC,
                Admin.class
        );
        // Admin 实体直接用于行数据，因为它在这里不包含像密码这样的敏感信息
        return new PageResult(adminPage.getTotalElements(), new ArrayList<>(adminPage.getContent()));
    }

    @Override
    public Admin getAdminById(String id) {
        logger.info("Service: 根据ID查询管理员, ID: {}", id);
        Admin admin = mongoDBUtils.findById(id, Admin.class);
        if (admin == null) {
            throw new EntityNotFoundException("未找到ID为 " + id + " 的管理员");
        }
        return admin;
    }

    @Override
    public Admin addAdmin(AdminSaveDTO dto) {
        logger.info("Service: 新增管理员, 数据: {}", dto);
        if (!StringUtils.hasText(dto.getUsername())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (!StringUtils.hasText(dto.getPassword())) {
            throw new IllegalArgumentException("密码不能为空");
        }

        // Check for username uniqueness
        if (adminReposity.findByUsername(dto.getUsername()) != null) {
            throw new DuplicateKeyException("用户名 '" + dto.getUsername() + "' 已存在");
        }

        Admin admin = new Admin();
        admin.setUsername(dto.getUsername());
        admin.setPassword(dto.getPassword()); // 目前按原样存储

        admin.setName(dto.getName());
        admin.setEnabled(dto.getEnabled() != null ? dto.getEnabled() : true); // 如果未指定，默认为 true

        Admin savedAdmin = mongoDBUtils.save(admin); // 或者 adminReposity.save(admin)
        
        // 返回给控制器时，好的做法是不包含密码
        // 即使没有序列化，如果服务返回的对象不包含密码也更好。
        // 然而，当前返回的是 Admin 对象。如果要求严格，可以创建一个新的 Admin 或 VO。
        savedAdmin.setPassword(null); // 为返回的对象屏蔽密码
        return savedAdmin;
    }

    @Override
    public void updateAdmin(String id, AdminSaveDTO dto) {
        logger.info("Service: 修改管理员 ID: {}, 数据: {}", id, dto);

        Admin admin = mongoDBUtils.findById(id, Admin.class);
        if (admin == null) {
            throw new EntityNotFoundException("未找到ID为 " + id + " 的管理员");
        }

        logger.debug("更新前管理员信息: username={}, name={}, enabled={}",
                    admin.getUsername(), admin.getName(), admin.getEnabled());

        boolean modified = false;

        // 检查策略列表是否为空
        if (updateAdminStrategies == null || updateAdminStrategies.isEmpty()) {
            logger.error("更新策略列表为空！无法执行更新操作");
            throw new IllegalStateException("更新策略列表为空");
        }

        logger.debug("开始执行 {} 个更新策略", updateAdminStrategies.size());

        // 遍历所有策略并应用更新
        for (UpdateAdminStrategy strategy : updateAdminStrategies) {
            logger.debug("执行策略: {}", strategy.getClass().getSimpleName());
            try {
                boolean strategyModified = strategy.update(admin, dto);
                if (strategyModified) {
                    logger.debug("策略 {} 执行成功，有修改", strategy.getClass().getSimpleName());
                    modified = true;
                } else {
                    logger.debug("策略 {} 执行完成，无修改", strategy.getClass().getSimpleName());
                }
            } catch (Exception e) {
                logger.error("策略 {} 执行失败: {}", strategy.getClass().getSimpleName(), e.getMessage(), e);
                throw e;
            }
        }

        logger.debug("更新后管理员信息: username={}, name={}, enabled={}",
                    admin.getUsername(), admin.getName(), admin.getEnabled());

        if (modified) {
            Admin savedAdmin = mongoDBUtils.save(admin);
            if (savedAdmin != null) {
                logger.info("Service: 管理员 ID: {} 已成功更新到数据库", id);
            } else {
                logger.error("Service: 管理员 ID: {} 保存到数据库失败", id);
                throw new RuntimeException("管理员信息保存失败");
            }
        } else {
            logger.info("Service: 管理员 ID: {} 未作任何修改", id);
        }
    }

    @Override
    public void deleteAdmins(List<String> adminIds , String currentAdminId ) {
        if (adminIds == null || adminIds.isEmpty()) {
            logger.info("Service: 管理员ID列表为空，无需删除");
            return;
        }
        logger.info("Service: 批量删除管理员, 原始IDs: {}", adminIds);

        List<String> idsToDelete = adminIds.stream()
                .filter(id -> {
                    if (id.equals(SYSTEM_ADMIN_ID)) {
                        throw new DeleteException("Service: 尝试删除系统管理员操作被阻止.");
                    }
                     //如果传递了 currentAdminId，则用于自我删除检查的占位符
                     if (id.equals(currentAdminId)) {
                         throw new DeleteException("Service: 管理员 (ID: {}) 尝试自我删除, 操作被阻止.");
                     }
                    return true;
                })
                .collect(Collectors.toList());

        // 另外，即使ID更改但用户名仍为"admin"，也要确保系统管理员不被删除
        // 这是次要检查；主要检查是通过ID。
        if (!idsToDelete.isEmpty()) {
            Iterable<Admin> iterableAdmins = adminReposity.findAllById(idsToDelete);
            List<Admin> adminsPotentiallyToDelete = StreamSupport.stream(iterableAdmins.spliterator(), false)
                .collect(Collectors.toList());
            
            List<String> finalIdsToDelete = adminsPotentiallyToDelete.stream()
                .filter(admin -> {
                    if (SYSTEM_ADMIN_USERNAME.equals(admin.getUsername())) {
                        //logger.warn("Service: 尝试删除系统管理员 (Username: {}), 操作被阻止.", admin.getUsername());
                        throw new DeleteException("Service: 尝试删除系统管理员, 操作被阻止.");
                    }
                    return true;
                })
                .map(Admin::getID)
                .collect(Collectors.toList());
            
            if (!finalIdsToDelete.isEmpty()) {
                adminReposity.deleteAllById(finalIdsToDelete); // deleteAllById 由 MongoRepository 提供
                logger.info("Service: 成功删除管理员IDs: {}", finalIdsToDelete);
            } else {
                logger.info("Service: 过滤后没有管理员可删除 (已排除系统管理员和/或自我删除尝试).");
            }
        } else {
            logger.info("Service: 过滤后没有管理员可删除 (原始列表可能只包含受保护的管理员).");
        }
    }
}
