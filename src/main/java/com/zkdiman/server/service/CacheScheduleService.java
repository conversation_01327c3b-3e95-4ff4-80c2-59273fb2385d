package com.zkdiman.server.service;

import com.zkdiman.server.service.Impl.StudentServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 缓存定时清理服务
 * 负责定时清理各种缓存，防止缓存数据过期或不一致
 */
@Service
public class CacheScheduleService {
    
    private static final Logger logger = LogManager.getLogger(CacheScheduleService.class);
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private StudentServiceImpl studentService;
    
    /**
     * 每天凌晨2点清除所有学生相关缓存
     * 防止缓存数据与数据库不一致
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void clearStudentCacheDaily() {
        logger.info("开始执行每日缓存清理任务...");
        
        try {
            // 清除学生相关缓存
            studentService.clearAllStudentCache();
            
            // 清除其他可能的缓存
            clearOtherCaches();
            
            logger.info("每日缓存清理任务执行完成");
        } catch (Exception e) {
            logger.error("每日缓存清理任务执行失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清除其他系统缓存
     */
    private void clearOtherCaches() {
        try {
            // 清除各种系统缓存
            String[] otherCaches = {"examCache", "taskCache", "schoolCache", "adminCache"};

            for (String cacheName : otherCaches) {
                if (cacheManager.getCache(cacheName) != null) {
                    cacheManager.getCache(cacheName).clear();
                    logger.info("已清除缓存: {}", cacheName);
                }
            }

        } catch (Exception e) {
            logger.warn("清除其他缓存时发生异常: {}", e.getMessage());
        }
    }
    
    /**
     * 手动触发缓存清理
     * 可以通过管理接口调用
     */
    public void manualClearAllCache() {
        logger.info("手动触发缓存清理...");
        
        try {
            // 清除所有缓存
            cacheManager.getCacheNames().forEach(cacheName -> {
                try {
                    if (cacheManager.getCache(cacheName) != null) {
                        cacheManager.getCache(cacheName).clear();
                        logger.info("已清除缓存: {}", cacheName);
                    }
                } catch (Exception e) {
                    logger.warn("清除缓存 {} 时发生异常: {}", cacheName, e.getMessage());
                }
            });
            
            logger.info("手动缓存清理完成");
        } catch (Exception e) {
            logger.error("手动缓存清理失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取缓存统计信息
     * 用于监控缓存使用情况
     */
    public String getCacheStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("缓存统计信息:\n");
        
        try {
            cacheManager.getCacheNames().forEach(cacheName -> {
                try {
                    if (cacheManager.getCache(cacheName) != null) {
                        // 这里可以根据具体的缓存实现获取更详细的统计信息
                        stats.append(String.format("- %s: 已配置\n", cacheName));
                    }
                } catch (Exception e) {
                    stats.append(String.format("- %s: 获取统计信息失败\n", cacheName));
                }
            });
        } catch (Exception e) {
            stats.append("获取缓存统计信息失败: ").append(e.getMessage());
        }
        
        return stats.toString();
    }
}
