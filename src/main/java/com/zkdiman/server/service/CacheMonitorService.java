package com.zkdiman.server.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.ConcurrentMap;

/**
 * 缓存监控服务
 * 监控缓存大小和内存使用情况，防止缓存溢出
 */
@Service
public class CacheMonitorService {
    
    private static final Logger logger = LogManager.getLogger(CacheMonitorService.class);
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private CacheScheduleService cacheScheduleService;
    
    // 缓存大小阈值配置
    private static final int MAX_CACHE_SIZE = 10000;           // 单个缓存最大条目数
    private static final double MEMORY_THRESHOLD = 0.85;       // 内存使用率阈值 85%
    private static final double CRITICAL_MEMORY_THRESHOLD = 0.95; // 临界内存使用率 95%
    
    /**
     * 每5分钟检查一次缓存大小和内存使用情况
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void monitorCacheSize() {
        try {
            logger.debug("开始缓存大小监控...");
            
            // 检查内存使用情况
            MemoryUsage memoryUsage = getMemoryUsage();
            double memoryUsagePercent = (double) memoryUsage.getUsed() / memoryUsage.getMax();
            
            logger.debug("当前内存使用率: {:.2f}%", memoryUsagePercent * 100);
            
            // 如果内存使用率超过临界阈值，立即清理所有缓存
            if (memoryUsagePercent > CRITICAL_MEMORY_THRESHOLD) {
                logger.warn("内存使用率达到临界值 {:.2f}%，立即清理所有缓存", memoryUsagePercent * 100);
                cacheScheduleService.manualClearAllCache();
                return;
            }
            
            // 如果内存使用率超过普通阈值，检查并清理大缓存
            if (memoryUsagePercent > MEMORY_THRESHOLD) {
                logger.warn("内存使用率超过阈值 {:.2f}%，开始清理大缓存", memoryUsagePercent * 100);
                clearOversizedCaches();
                return;
            }
            
            // 正常情况下检查缓存大小
            checkCacheSizes();
            
        } catch (Exception e) {
            logger.error("缓存监控过程中发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 每小时检查一次缓存大小（更频繁的检查）
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void hourlyMonitor() {
        try {
            logger.info("开始每小时缓存监控...");
            
            // 获取详细的缓存统计信息
            CacheStatistics stats = getCacheStatistics();
            logger.info("缓存统计: {}", stats);
            
            // 如果总缓存条目数过多，进行清理
            if (stats.getTotalEntries() > MAX_CACHE_SIZE * 2) {
                logger.warn("总缓存条目数 {} 超过阈值，开始清理", stats.getTotalEntries());
                clearOversizedCaches();
            }
            
        } catch (Exception e) {
            logger.error("每小时缓存监控过程中发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查各个缓存的大小
     */
    private void checkCacheSizes() {
        cacheManager.getCacheNames().forEach(cacheName -> {
            try {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache instanceof ConcurrentMapCache) {
                    ConcurrentMapCache concurrentMapCache = (ConcurrentMapCache) cache;
                    ConcurrentMap<Object, Object> nativeCache = concurrentMapCache.getNativeCache();
                    int size = nativeCache.size();
                    
                    logger.debug("缓存 {} 当前大小: {}", cacheName, size);
                    
                    if (size > MAX_CACHE_SIZE) {
                        logger.warn("缓存 {} 大小 {} 超过阈值 {}，开始清理", cacheName, size, MAX_CACHE_SIZE);
                        cache.clear();
                        logger.info("已清理缓存: {}", cacheName);
                    }
                }
            } catch (Exception e) {
                logger.warn("检查缓存 {} 大小时发生异常: {}", cacheName, e.getMessage());
            }
        });
    }
    
    /**
     * 清理过大的缓存
     */
    private void clearOversizedCaches() {
        cacheManager.getCacheNames().forEach(cacheName -> {
            try {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache instanceof ConcurrentMapCache) {
                    ConcurrentMapCache concurrentMapCache = (ConcurrentMapCache) cache;
                    ConcurrentMap<Object, Object> nativeCache = concurrentMapCache.getNativeCache();
                    int size = nativeCache.size();
                    
                    // 如果缓存大小超过阈值的一半，就清理
                    if (size > MAX_CACHE_SIZE / 2) {
                        logger.info("清理过大缓存: {} (大小: {})", cacheName, size);
                        cache.clear();
                    }
                }
            } catch (Exception e) {
                logger.warn("清理缓存 {} 时发生异常: {}", cacheName, e.getMessage());
            }
        });
    }
    
    /**
     * 获取内存使用情况
     */
    private MemoryUsage getMemoryUsage() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        return memoryBean.getHeapMemoryUsage();
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        CacheStatistics stats = new CacheStatistics();
        
        cacheManager.getCacheNames().forEach(cacheName -> {
            try {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache instanceof ConcurrentMapCache) {
                    ConcurrentMapCache concurrentMapCache = (ConcurrentMapCache) cache;
                    ConcurrentMap<Object, Object> nativeCache = concurrentMapCache.getNativeCache();
                    int size = nativeCache.size();
                    
                    stats.addCacheInfo(cacheName, size);
                }
            } catch (Exception e) {
                logger.warn("获取缓存 {} 统计信息时发生异常: {}", cacheName, e.getMessage());
            }
        });
        
        // 添加内存使用信息
        MemoryUsage memoryUsage = getMemoryUsage();
        stats.setMemoryUsage(memoryUsage);
        
        return stats;
    }
    
    /**
     * 手动触发缓存大小检查
     */
    public void manualCheckCacheSize() {
        logger.info("手动触发缓存大小检查...");
        monitorCacheSize();
    }
    
    /**
     * 缓存统计信息类
     */
    public static class CacheStatistics {
        private int totalEntries = 0;
        private StringBuilder details = new StringBuilder();
        private MemoryUsage memoryUsage;
        
        public void addCacheInfo(String cacheName, int size) {
            totalEntries += size;
            details.append(String.format("- %s: %d 条目\n", cacheName, size));
        }
        
        public int getTotalEntries() {
            return totalEntries;
        }
        
        public void setMemoryUsage(MemoryUsage memoryUsage) {
            this.memoryUsage = memoryUsage;
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("缓存统计信息:\n");
            sb.append(details);
            sb.append(String.format("总条目数: %d\n", totalEntries));
            
            if (memoryUsage != null) {
                double usagePercent = (double) memoryUsage.getUsed() / memoryUsage.getMax() * 100;
                sb.append(String.format("内存使用: %.2f%% (%d MB / %d MB)\n", 
                    usagePercent,
                    memoryUsage.getUsed() / 1024 / 1024,
                    memoryUsage.getMax() / 1024 / 1024));
            }
            
            return sb.toString();
        }
    }
}
