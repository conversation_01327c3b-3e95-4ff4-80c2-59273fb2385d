package com.zkdiman.server.service;

import com.zkdiman.common.result.PageResult;
import com.zkdiman.pojo.dto.exam.ExamAddDTO;
import com.zkdiman.pojo.dto.exam.ExamQueryDTO;
import com.zkdiman.pojo.dto.exam.ExamUpdateDTO;
import com.zkdiman.pojo.dto.exam.ExamStudentQueryDTO;
import com.zkdiman.pojo.dto.exam.ExamAddStudentsDTO;
import com.zkdiman.pojo.dto.student.StudentTimeAdjustmentDTO;
import com.zkdiman.pojo.entity.Exam; // DTO for query, Entity for result items
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

@Validated
public interface ExamService {

    /**
     * 考试场次列表查询（分页）
     * @param examQueryDTO 查询条件
     * @return 分页结果，其中包含 Exam 实体列表
     */
    PageResult getExamListPage(@Valid ExamQueryDTO examQueryDTO);

    void addExam(ExamAddDTO examAddDTO);

    Exam getExamById(String id);

    void updateExam(String id, ExamUpdateDTO examUpdateDTO);

    void deleteExamById(String id);

    PageResult getStudentsByExamId(String examId, ExamStudentQueryDTO examStudentQueryDTO);

    void addStudentsToExam(String examId, ExamAddStudentsDTO examAddStudentsDTO);

    void removeStudentsFromExam(String examId, List<String> studentIds);

    /**
     * 为单个学生调整考试时间
     * @param examId 考试场次ID
     * @param studentId 学生在考试中的记录ID (CompetitionSignUpLog ID)
     * @param studentTimeAdjustmentDTO 调整的时间信息
     */
    void adjustStudentExamTime(String examId, String studentId, StudentTimeAdjustmentDTO studentTimeAdjustmentDTO);

    /**
     * 通过Excel批量导入学员到场次并分配考试时间
     *
     * @param examId 考试场次ID
     * @param file Excel文件
     * @param startTime 统一设置的考试开始时间 (Epoch毫秒)
     * @param duration 统一设置的考试时长 (分钟)
     * @return 批量导入结果
     */
    BatchImportResultVO batchImportAssignedStudents(String examId, MultipartFile file, Long startTime, Integer duration);


} 