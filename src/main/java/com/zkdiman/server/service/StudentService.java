package com.zkdiman.server.service;

import com.zkdiman.common.result.PageResult;
import com.zkdiman.pojo.dto.student.StudentPageDTO;
import com.zkdiman.pojo.dto.student.StudentScreenshotSubmitDTO;
import com.zkdiman.pojo.dto.student.SelectableStudentsQueryDTO;
import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import com.zkdiman.pojo.vo.student.StudentLoginVo;
import com.zkdiman.pojo.vo.student.StudentQueryVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

@Validated
public interface StudentService {
    /**
     * 学生登录
     * @param examCard 准考证号
     * @return StudentLoginVo 同步结果
     */
    StudentLoginVo login(@NotBlank(message = "准考证号不能为空") String examCard);

    /**
     * 提交答题截图URL
     * @param submitDTO 包含准考证号和截图URL的DTO
     */
    CompletableFuture<Void> submitScreenshotUrl(@Valid StudentScreenshotSubmitDTO submitDTO);

    /**
     * 完成考试
     * @param examCard 准考证号
     * @param submitType 提交类型
     * @return 学生姓名
     */
    CompletableFuture<String> completed(@NotBlank(message = "准考证号不能为空") String examCard,
                     @NotNull(message = "提交类型不能为空") Integer submitType);

    /**
     * 学生列表分页查询
     * @param studentPageDto
     * @return
     */
    PageResult getStudentListPage(@Valid StudentPageDTO studentPageDto);

    StudentQueryVO getStudentById(String id);

    void addOrUpdateStudent(StudentQueryVO studentQueryVO);

    void deleteStudent(String id);

    /**
     * 批量导入学生账号
     * @param file Excel文件
     * @return 导入结果
     */
    BatchImportResultVO batchImportStudents(@NotNull(message = "导入文件不能为空") MultipartFile file);

    /**
     * 查询可被添加到指定考试场次的学员列表
     * @param queryDTO 查询参数
     * @return 分页的学员列表 (CompetitionSignUpLog)
     */
    PageResult getSelectableStudents(@Valid SelectableStudentsQueryDTO queryDTO);

    /**
     * 根据学生准考证号查询其所有答题截图URL列表
     * @param examCard 学生准考证号
     * @return 截图信息列表, 每个元素包含一个试题的截图URL列表
     */
    ArrayList<Answer> getScreenshotsByExamCard(String examCard);

    /**
     * 清除所有学生相关缓存
     */
    void clearAllStudentCache();
}
