package com.zkdiman.server.service;

import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;

import java.util.List;

/**
 * 答题记录初始化服务接口
 * 负责为学生初始化答题记录表并绑定考场
 */
public interface QuestionLogInitService {
    
    /**
     * 为单个学生初始化答题记录
     * 
     * @param student 学生信息
     * @return 初始化的答题记录
     */
    CompetitionQuestionLog initQuestionLogForStudent(CompetitionSignUpLog student);
    
    /**
     * 为多个学生批量初始化答题记录
     * 
     * @param students 学生信息列表
     * @return 初始化成功的数量
     */
    int batchInitQuestionLogsForStudents(List<CompetitionSignUpLog> students);
    
    /**
     * 检查学生是否已有答题记录
     * 
     * @param examCard 准考证号
     * @param examId 考试ID
     * @return 是否已存在答题记录
     */
    boolean hasQuestionLog(String examCard, String examId);
    
    /**
     * 为学生创建或更新答题记录
     * 如果已存在则更新考试ID，如果不存在则创建新记录
     * 
     * @param student 学生信息
     * @return 答题记录
     */
    CompetitionQuestionLog createOrUpdateQuestionLog(CompetitionSignUpLog student);
}
