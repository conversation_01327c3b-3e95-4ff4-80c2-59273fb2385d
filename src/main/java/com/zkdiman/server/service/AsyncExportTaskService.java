package com.zkdiman.server.service;

import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.ExportTask;

/**
 * 异步导出任务服务接口
 * 专门处理异步导出任务的执行
 */
public interface AsyncExportTaskService {
    
    /**
     * 异步执行导出任务
     * 
     * @param taskId 任务ID
     * @param examId 考试场次ID
     * @param taskType 任务类型
     * @param exam 考试实体
     * @param exportTask 导出任务实体
     */
    void executeExportTaskAsync(String taskId, String examId, String taskType, Exam exam, ExportTask exportTask);
}
