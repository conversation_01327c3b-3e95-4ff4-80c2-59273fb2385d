package com.zkdiman.server.service;

import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;

import java.util.ArrayList;

/**
 * 答题记录提交服务接口
 * 负责处理学生答题记录的提交和更新逻辑
 */
public interface AnswerSubmissionService {
    
    /**
     * 添加或更新学生答题记录
     * 如果相同的questionId和questionType已存在，则覆盖；否则新增
     * 
     * @param studentAnswers 学生答题记录列表
     * @param newAnswer 新的答题记录
     * @param examCard 准考证号（用于日志）
     * @return 更新后的答题记录列表
     */
    ArrayList<Answer> addOrUpdateAnswer(ArrayList<Answer> studentAnswers, Answer newAnswer, String examCard);
    
    /**
     * 检查是否存在相同的题目答案
     * 
     * @param studentAnswers 学生答题记录列表
     * @param questionId 题目ID
     * @param questionType 题目类型
     * @return 如果存在返回索引，否则返回-1
     */
    int findExistingAnswerIndex(ArrayList<Answer> studentAnswers, String questionId, Integer questionType);
    
    /**
     * 验证答题记录的有效性
     * 
     * @param answer 答题记录
     * @return 是否有效
     */
    boolean validateAnswer(Answer answer);
    
    /**
     * 获取答题记录的唯一标识
     * 
     * @param answer 答题记录
     * @return 唯一标识字符串
     */
    String getAnswerKey(Answer answer);
}
