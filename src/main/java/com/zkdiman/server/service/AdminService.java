package com.zkdiman.server.service;

import com.zkdiman.common.result.PageResult;
import com.zkdiman.pojo.dto.admin.AdminQueryDTO;
import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.vo.admin.AdminLoginVO;
import com.zkdiman.pojo.entity.Admin;

import javax.validation.Valid;
import java.util.List;

public interface AdminService {
    AdminLoginVO login(String username, String password);

    /**
     * 管理员列表查询（分页）
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult getAdminListPage(@Valid AdminQueryDTO queryDTO);

    /**
     * 根据ID获取单个管理员信息
     * @param id 管理员ID
     * @return Admin实体 (不应包含密码传到前端)
     */
    Admin getAdminById(String id);

    /**
     * 新增管理员信息
     * @param dto 管理员信息 DTO
     * @return 创建后的Admin实体 (不含密码，用于Controller返回)
     */
    Admin addAdmin(@Valid AdminSaveDTO dto);

    /**
     * 修改管理员信息
     * @param id 管理员ID
     * @param dto 更新的管理员信息 DTO
     */
    void updateAdmin(String id, @Valid AdminSaveDTO dto);

    /**
     * 批量删除管理员
     * @param adminIds 要删除的管理员ID列表
     * // @param currentAdminId 当前操作的管理员ID (用于防止自我删除)
     */
    void deleteAdmins(List<String> adminIds , String currentAdminId );
}
