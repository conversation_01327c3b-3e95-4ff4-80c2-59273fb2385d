package com.zkdiman.server.service;

import com.zkdiman.pojo.vo.admin.ExportTaskStatusVO;


public interface ExamExportService {

    /**
     * 启动异步导出指定考试场次下所有学员的所有答题截图的任务
     *
     * @param examId   考试场次ID
     * @return 任务ID
     */
    String initiateExamScreenshotsExport(String examId);

    /**
     * 查询异步导出任务的状态
     *
     * @param taskId 任务ID
     * @return 任务状态及结果
     */
    ExportTaskStatusVO getExportTaskStatus(String taskId);

}
