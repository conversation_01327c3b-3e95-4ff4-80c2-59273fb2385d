package com.zkdiman.server.service;

import com.zkdiman.pojo.dto.ExamLogExportDTO;
import com.zkdiman.pojo.vo.admin.ExportTaskStatusVO;

import java.util.List;


public interface ExamExportService {

    /**
     * 启动异步导出指定考试场次下所有学员的所有答题截图的任务
     *
     * @param examId   考试场次ID
     * @return 任务ID
     */
    String initiateExamScreenshotsExport(String examId);

    /**
     * 查询异步导出任务的状态
     *
     * @param taskId 任务ID
     * @return 任务状态及结果
     */
    ExportTaskStatusVO getExportTaskStatus(String taskId);


    /**
     * 根据考场ID，生成用于Excel导出的数据列表。
     *
     * @param examId 考场ID
     * @return 转换并聚合后的DTO列表，可直接用于Excel导出
     */
    public List<ExamLogExportDTO> generateExportDataByExamId(String examId);
}
