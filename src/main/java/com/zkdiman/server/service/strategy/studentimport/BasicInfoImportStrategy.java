package com.zkdiman.server.service.strategy.studentimport;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 学生基本信息导入策略
 * 处理学生的基本个人信息导入
 */
@Component
public class BasicInfoImportStrategy implements StudentImportStrategy {

    @Override
    public boolean processImport(StudentExcelDataDTO studentData, CompetitionSignUpLog signUpLog, 
                               MongoDBUtils mongoDBUtils, Logger logger) {
        logger.debug("Applying BasicInfoImportStrategy for student: {}", studentData.getExamCard());
        
        try {
            // 设置考生基本信息
            if (StringUtils.hasText(studentData.getGrade())) {
                signUpLog.setGrade(studentData.getGrade());
            }
            
            if (StringUtils.hasText(studentData.getClassName())) {
                signUpLog.setClassName(studentData.getClassName());
            }
            
            signUpLog.setName(studentData.getName());
            
            if (studentData.getGender() != null) {
                signUpLog.setGender(studentData.getGender());
            }
            
            if (StringUtils.hasText(studentData.getIdCard())) {
                signUpLog.setIdCard(studentData.getIdCard());
            }
            
            if (StringUtils.hasText(studentData.getNation())) {
                signUpLog.setNation(studentData.getNation());
            }
            
            if (studentData.getStudyMode() != null) {
                signUpLog.setStudyMode(studentData.getStudyMode());
            }
            
            if (studentData.getHouseholdType() != null) {
                signUpLog.setHouseholdType(studentData.getHouseholdType());
            }
            
            return true;
        } catch (Exception e) {
            logger.error("Error processing basic information for student: {}, error: {}", 
                        studentData.getExamCard(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getOrder() {
        // 基本信息应该最先处理
        return 1;
    }
} 