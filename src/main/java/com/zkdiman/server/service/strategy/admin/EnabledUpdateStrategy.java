package com.zkdiman.server.service.strategy.admin;


import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.entity.Admin;
import org.springframework.stereotype.Component;

@Component
public class EnabledUpdateStrategy implements UpdateAdminStrategy {

    @Override
    public boolean update(Admin admin, AdminSaveDTO dto) {
        if (dto.getEnabled() != null && !dto.getEnabled().equals(admin.getEnabled())) {
            admin.setEnabled(dto.getEnabled());
            return true;
        }
        return false;
    }
}