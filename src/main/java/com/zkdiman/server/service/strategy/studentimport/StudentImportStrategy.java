package com.zkdiman.server.service.strategy.studentimport;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;

/**
 * 学生Excel数据导入策略接口
 * 用于定义不同类型的学生数据导入处理策略
 */
public interface StudentImportStrategy {

    /**
     * 处理导入学生数据
     *
     * @param studentData Excel中的学生数据
     * @param signUpLog 学生报名记录对象
     * @param mongoDBUtils MongoDB操作工具
     * @param logger 日志记录器
     * @return 是否处理成功
     */
    boolean processImport(StudentExcelDataDTO studentData, CompetitionSignUpLog signUpLog, 
                        MongoDBUtils mongoDBUtils, Logger logger);

    /**
     * 获取策略优先级，数字越小优先级越高
     * 用于策略执行顺序排序
     *
     * @return 优先级数值
     */
    int getOrder();
} 