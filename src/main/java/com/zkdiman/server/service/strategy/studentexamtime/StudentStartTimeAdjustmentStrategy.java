package com.zkdiman.server.service.strategy.studentexamtime;

import com.zkdiman.pojo.dto.student.StudentTimeAdjustmentDTO;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class StudentStartTimeAdjustmentStrategy implements StudentExamTimeAdjustmentStrategy {
    @Override
    public void applyAdjustment(Exam exam, CompetitionSignUpLog signUpLog, StudentTimeAdjustmentDTO dto, Map<String, Object> updateMap, Logger logger) {
        if (dto.getStartTime() != null) {
            if (exam.getStartTime() != null && dto.getStartTime() < exam.getStartTime()) {
                logger.warn("学生调整后的开始时间 {} 早于场次开始时间 {}", dto.getStartTime(), exam.getStartTime());
            }
            updateMap.put("startTime", dto.getStartTime());
        }
    }
} 