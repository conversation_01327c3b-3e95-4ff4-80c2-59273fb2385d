package com.zkdiman.server.service.strategy.studentimport;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

/**
 * 成绩信息导入策略
 * 处理学生的成绩信息导入
 */
@Component
public class ScoreInfoImportStrategy implements StudentImportStrategy {

    @Override
    public boolean processImport(StudentExcelDataDTO studentData, CompetitionSignUpLog signUpLog, 
                               MongoDBUtils mongoDBUtils, Logger logger) {
        logger.debug("Applying ScoreInfoImportStrategy for student: {}", studentData.getExamCard());
        
        try {
            // 设置成绩相关信息
            if (studentData.getScores() != null && studentData.getScores().length > 0) {
                signUpLog.setScores(studentData.getScores());
                logger.debug("Scores set for student: {}, count: {}", 
                          studentData.getExamCard(), studentData.getScores().length);
            }
            
            return true;
        } catch (Exception e) {
            logger.error("Error processing score information for student: {}, error: {}", 
                       studentData.getExamCard(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getOrder() {
        // 成绩信息应该最后处理
        return 4;
    }
} 