package com.zkdiman.server.service.strategy.studentexamtime;

import com.zkdiman.common.exception.BusinessException;
import com.zkdiman.pojo.dto.student.StudentTimeAdjustmentDTO;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class StudentDurationAdjustmentStrategy implements StudentExamTimeAdjustmentStrategy {
    @Override
    public void applyAdjustment(Exam exam, CompetitionSignUpLog signUpLog, StudentTimeAdjustmentDTO dto, Map<String, Object> updateMap, Logger logger) {
        if (dto.getDuration() != null) {
            if (dto.getDuration() <= 0) {
                throw new BusinessException("考试时长必须为正数");
            }
            updateMap.put("duration", dto.getDuration());
        }
    }
} 