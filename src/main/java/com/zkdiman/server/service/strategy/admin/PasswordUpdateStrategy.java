package com.zkdiman.server.service.strategy.admin;

import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.entity.Admin;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class PasswordUpdateStrategy implements UpdateAdminStrategy {
    public boolean update(Admin admin, AdminSaveDTO dto) {
        if (StringUtils.hasText(dto.getPassword())) {
            admin.setPassword(dto.getPassword()); // 目前按原样存储
            return true;
        }
        return false;
    }
} 