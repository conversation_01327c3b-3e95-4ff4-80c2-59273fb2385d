package com.zkdiman.server.service.strategy.export;

import com.zkdiman.common.constant.TaskConstant;
import com.zkdiman.common.utils.ExportUtils;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.common.utils.TencentCosUtils;
import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import com.zkdiman.pojo.entity.Exam;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 截图导出策略实现
 * 负责导出考试场次下所有学员的答题截图，并上传到腾讯云COS
 */
@Component
public class ScreenshotExportStrategy implements ExportStrategy {

    private static final Logger logger = LogManager.getLogger(ScreenshotExportStrategy.class);

    @Autowired
    private MongoDBUtils mongoDBUtils;

    @Autowired
    private TencentCosUtils tencentCosUtils;
    
    @Override
    public ExportResult execute(ExportContext context) throws Exception {
        logger.info("开始执行截图导出策略，任务ID: {}, 考试场次ID: {}",
                   context.getTaskId(), context.getExamId());

        Exam exam = context.getExam();
        String examNameSafe = ExportUtils.createSafeFileName(exam.getExamName(), "exam") + "_" + context.getExamId();
        String zipFileName = examNameSafe + "_screenshots.zip";

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipArchiveOutputStream zos = new ZipArchiveOutputStream(baos)) {
            
            // 获取考试场次下的所有学员
            List<CompetitionSignUpLog> studentsInExam = mongoDBUtils.find(
                Criteria.where("examID").is(context.getExamId()), 
                CompetitionSignUpLog.class
            );
            
            if (studentsInExam.isEmpty()) {
                logger.info("考试场次 {} 没有学员参加，生成空提示文件", context.getExamId());
                ExportUtils.addTextFileToZip(zos, "no_screenshots_found.txt", 
                    "此考试场次没有学员或没有截图数据.");
            } else {
                boolean hasAnyScreenshot = processStudentScreenshots(zos, studentsInExam, context);
                
                if (!hasAnyScreenshot) {
                    logger.info("考试场次 {} 有学员但没有任何截图，生成提示信息", context.getExamId());
                    ExportUtils.addTextFileToZip(zos, "no_screenshots_found.txt", 
                        "此考试场次的所有学员均未找到有效截图.");
                }
            }
            
            zos.finish();
            byte[] zipBytes = baos.toByteArray();

            // 上传到腾讯云COS
            String fileUrl = tencentCosUtils.uploadFile(zipBytes, zipFileName, "application/zip");
            if (fileUrl == null) {
                throw new RuntimeException("文件上传到腾讯云COS失败");
            }

            logger.info("截图导出完成，任务ID: {}, 文件名: {}, 文件大小: {} bytes, COS URL: {}",
                       context.getTaskId(), zipFileName, zipBytes.length, fileUrl);

            return new ExportResult(fileUrl, zipFileName);
        }
    }
    
    /**
     * 处理学员截图数据
     */
    private boolean processStudentScreenshots(ZipArchiveOutputStream zos,
                                              List<CompetitionSignUpLog> studentsInExam,
                                              ExportContext context) throws IOException {
        boolean hasAnyScreenshot = false;
        
        for (CompetitionSignUpLog student : studentsInExam) {
            String studentFolderName = ExportUtils.createSafeFileName(student.getName(), "unknown_student") 
                                     + "_" + student.getExamCard();
            int screenshotFileCounter = 1;
            boolean studentFolderCreated = false;
            
            // 获取学员的答题记录
            Criteria questionLogCriteria = Criteria.where("ExamId").is(context.getExamId())
                    .and("examCard").is(student.getExamCard());
            List<CompetitionQuestionLog> questionLogs = mongoDBUtils.find(questionLogCriteria, CompetitionQuestionLog.class);

            logger.debug("查询学员 {} (准考证号: {}) 在考试 {} 中的答题记录，找到 {} 条记录",
                        student.getName(), student.getExamCard(), context.getExamId(), questionLogs.size());

            if (questionLogs.isEmpty()) {
                logger.info("学员 {} (准考证号: {}) 在考试 {} 中没有答题记录",
                           student.getName(), student.getExamCard(), context.getExamId());
                continue;
            }
            
            // 收集该学员的所有答题记录，用于统计分析
            List<Answer> allAnswers = new ArrayList<>();
            for (CompetitionQuestionLog qLog : questionLogs) {
                if (qLog.getStudentAnswer() != null && !qLog.getStudentAnswer().isEmpty()) {
                    allAnswers.addAll(qLog.getStudentAnswer());
                }
            }

            // 输出统计信息
            String statistics = ExportUtils.analyzeUrlStatistics(allAnswers);
            logger.info("学员 {} (准考证号: {}) URL统计: {}",
                       student.getName(), student.getExamCard(), statistics);

            // 收集该学员的所有唯一截图URL
            Set<String> uniqueUrls = new LinkedHashSet<>();
            Map<String, String> urlToQuestionMap = new HashMap<>();

            for (Answer answer : allAnswers) {
                if (answer.getUrl() != null && !answer.getUrl().trim().isEmpty()) {
                    uniqueUrls.add(answer.getUrl());
                    // 记录URL对应的题目ID（用于文件命名）
                    if (!urlToQuestionMap.containsKey(answer.getUrl())) {
                        urlToQuestionMap.put(answer.getUrl(), answer.getQuestionId());
                    }
                }
            }

            logger.debug("学员 {} (准考证号: {}) 共有 {} 个唯一截图URL",
                        student.getName(), student.getExamCard(), uniqueUrls.size());

            // 处理每个唯一的截图URL
            for (String imageUrl : uniqueUrls) {
                try {
                    byte[] imageBytes = ExportUtils.downloadImageFromUrl(imageUrl);
                    if (imageBytes != null && imageBytes.length > 0) {
                        hasAnyScreenshot = true;
                        studentFolderCreated = true;

                        // 使用题目ID和原始文件名生成新文件名
                        String questionId = urlToQuestionMap.get(imageUrl);
                        String originalFileName = ExportUtils.extractFileNameFromUrl(imageUrl);
                        String screenshotFileName = String.format("题目%s_%s", questionId, originalFileName);
                        String fullPath = studentFolderName + "/" + screenshotFileName;

                        ExportUtils.addImageFileToZip(zos, fullPath, imageBytes);
                        screenshotFileCounter++;

                        logger.debug("已添加截图: {} -> {}", imageUrl, screenshotFileName);
                    } else {
                        logger.warn("无法下载或图片为空 URL: {} (学员: {}, 任务: {})",
                                  imageUrl, student.getExamCard(), context.getTaskId());
                    }
                } catch (Exception e) {
                    logger.error("下载/处理截图失败 URL {}: {} (学员: {}, 任务: {})",
                               imageUrl, e.getMessage(), student.getExamCard(), context.getTaskId());
                }
            }
            
            if (!studentFolderCreated && !questionLogs.isEmpty()) {
                logger.info("学员 {} (准考证号: {}) 在考试 {} 中有 {} 条答题记录但无有效截图",
                          student.getName(), student.getExamCard(), context.getExamId(), questionLogs.size());
            } else if (studentFolderCreated) {
                logger.info("学员 {} (准考证号: {}) 成功导出 {} 个唯一截图",
                          student.getName(), student.getExamCard(), screenshotFileCounter - 1);
            }
        }
        
        return hasAnyScreenshot;
    }
    
    @Override
    public String getSupportedTaskType() {
        return TaskConstant.TASK_TYPE_EXAM_SCREENSHOTS;
    }
}
