package com.zkdiman.server.service.strategy.admin;


import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.entity.Admin;
import com.zkdiman.server.reposity.AdminReposity;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class UsernameUpdateStrategy implements UpdateAdminStrategy {

    private final AdminReposity adminReposity;

    public UsernameUpdateStrategy(AdminReposity adminReposity) {
        this.adminReposity = adminReposity;
    }

    @Override
    public boolean update(Admin admin, AdminSaveDTO dto) {
        if (StringUtils.hasText(dto.getUsername()) && !dto.getUsername().equals(admin.getUsername())) {
            // 如果要更改用户名，请检查唯一性
            if (adminReposity.findByUsername(dto.getUsername()) != null) {
                throw new DuplicateKeyException("用户名 '" + dto.getUsername() + "' 已被其他管理员使用");
            }
            admin.setUsername(dto.getUsername());
            return true;
        }
        return false;
    }
} 