package com.zkdiman.server.service.strategy.exam;

import com.zkdiman.pojo.dto.exam.ExamUpdateDTO;
import com.zkdiman.pojo.entity.Exam;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ExamNameUpdateStrategy implements UpdateExamStrategy {
    @Override
    public void applyUpdate(Exam existingExam, ExamUpdateDTO dto, Map<String, Object> examUpdateMap, Map<String, Object> studentUpdateMap) {
        if (dto.getExamName() != null) {
            examUpdateMap.put("examName", dto.getExamName());
        }
    }
} 