package com.zkdiman.server.service.strategy.studentimport;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;

/**
 * 学生导入策略管理器
 * 负责组织和执行所有学生数据导入策略
 */
@Component
public class StudentImportStrategyManager {

    private final List<StudentImportStrategy> strategies;
    
    @Autowired
    public StudentImportStrategyManager(List<StudentImportStrategy> strategies) {
        this.strategies = strategies;
    }
    
    @PostConstruct
    public void init() {
        // 根据策略的优先级排序
        strategies.sort(Comparator.comparingInt(StudentImportStrategy::getOrder));
    }
    
    /**
     * 执行所有导入策略
     *
     * @param studentData Excel中的学生数据
     * @param signUpLog 学生报名记录对象
     * @param mongoDBUtils MongoDB操作工具
     * @param logger 日志记录器
     * @return 所有策略是否全部执行成功
     */
    public boolean executeStrategies(StudentExcelDataDTO studentData, CompetitionSignUpLog signUpLog, 
                                   MongoDBUtils mongoDBUtils, Logger logger) {
        boolean allSuccess = true;
        
        for (StudentImportStrategy strategy : strategies) {
            boolean success = strategy.processImport(studentData, signUpLog, mongoDBUtils, logger);
            if (!success) {
                logger.warn("Strategy {} failed for student: {}", 
                          strategy.getClass().getSimpleName(), studentData.getExamCard());
                allSuccess = false;
                // 继续执行其他策略，不中断流程
            }
        }
        
        return allSuccess;
    }
} 