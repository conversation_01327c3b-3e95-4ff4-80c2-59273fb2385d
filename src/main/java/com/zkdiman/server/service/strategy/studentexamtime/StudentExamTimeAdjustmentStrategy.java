package com.zkdiman.server.service.strategy.studentexamtime;

import com.zkdiman.pojo.dto.student.StudentTimeAdjustmentDTO;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 调整学员考试时间策略接口
 */
public interface StudentExamTimeAdjustmentStrategy {
    /**
     * 应用时间调整逻辑，并将需要更新的字段放入 updateMap。
     *
     * @param exam                   当前考试场次实体
     * @param signUpLog              学员的报名记录实体
     * @param dto                    学员时间调整DTO
     * @param updateMap              用于收集更新字段的Map
     * @param logger                 用于记录日志的Logger实例
     */
    void applyAdjustment(Exam exam, CompetitionSignUpLog signUpLog, StudentTimeAdjustmentDTO dto, Map<String, Object> updateMap, Logger logger);
} 