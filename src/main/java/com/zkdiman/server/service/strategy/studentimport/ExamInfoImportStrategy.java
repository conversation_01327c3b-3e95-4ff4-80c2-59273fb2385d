package com.zkdiman.server.service.strategy.studentimport;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 考试信息导入策略
 * 处理学生的考试相关信息导入
 */
@Component
public class ExamInfoImportStrategy implements StudentImportStrategy {

    @Override
    public boolean processImport(StudentExcelDataDTO studentData, CompetitionSignUpLog signUpLog, 
                               MongoDBUtils mongoDBUtils, Logger logger) {
        logger.debug("Applying ExamInfoImportStrategy for student: {}", studentData.getExamCard());
        
        try {
            // 设置考试相关信息
            if (StringUtils.hasText(studentData.getExamRoom())) {
                signUpLog.setExamRoom(studentData.getExamRoom());
            }
            
            if (StringUtils.hasText(studentData.getExamRoomNumber())) {
                signUpLog.setExamRoomNumber(studentData.getExamRoomNumber());
            }
            
            if (StringUtils.hasText(studentData.getSeatNumber())) {
                signUpLog.setSeatNumber(studentData.getSeatNumber());
            }
            
            if (StringUtils.hasText(studentData.getExamLocation())) {
                signUpLog.setExamLocation(studentData.getExamLocation());
            }
            
            if (studentData.getSubmitType() != null) {
                signUpLog.setSubmitType(studentData.getSubmitType());
            }
            
            return true;
        } catch (Exception e) {
            logger.error("Error processing exam information for student: {}, error: {}", 
                       studentData.getExamCard(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getOrder() {
        // 考试信息应该在学校信息之后处理
        return 3;
    }
} 