package com.zkdiman.server.service.strategy.exam;

import com.zkdiman.pojo.dto.exam.ExamUpdateDTO;
import com.zkdiman.pojo.entity.Exam;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ExamDurationUpdateStrategy implements UpdateExamStrategy {
    @Override
    public void applyUpdate(Exam existingExam, ExamUpdateDTO dto, Map<String, Object> examUpdateMap, Map<String, Object> studentUpdateMap) {
        if (dto.getDuration() != null) {
            examUpdateMap.put("duration", dto.getDuration());
            studentUpdateMap.put("duration", dto.getDuration()); // Also update for students
        }
    }
} 