package com.zkdiman.server.service.strategy.admin;


import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.entity.Admin;
import org.springframework.stereotype.Component;

@Component
public class NameUpdateStrategy implements UpdateAdminStrategy {

    @Override
    public boolean update(Admin admin, AdminSaveDTO dto) {
        if (dto.getName() != null && !dto.getName().equals(admin.getName())) {
            admin.setName(dto.getName());
            return true;
        }
        return false;
    }
}