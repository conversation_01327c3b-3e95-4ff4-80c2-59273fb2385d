package com.zkdiman.server.service.strategy.studentimport;

import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.dto.student.StudentExcelDataDTO;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import com.zkdiman.pojo.entity.School;
import org.apache.logging.log4j.Logger;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 学校信息导入策略
 * 处理学生关联的学校信息导入
 */
@Component
public class SchoolInfoImportStrategy implements StudentImportStrategy {

    @Override
    public boolean processImport(StudentExcelDataDTO studentData, CompetitionSignUpLog signUpLog, 
                               MongoDBUtils mongoDBUtils, Logger logger) {
        logger.debug("Applying SchoolInfoImportStrategy for student: {}", studentData.getExamCard());
        
        try {
            // 设置学校名称到学生记录
            signUpLog.setSchool(studentData.getSchool());
            
            // 只有学校代码存在才进行学校信息处理
            if (!StringUtils.hasText(studentData.getSchoolCode())) {
                logger.warn("School code is empty for student: {}", studentData.getExamCard());
                return true; // 无学校代码也视为成功，不影响其他策略执行
            }
            
            signUpLog.setSchoolCode(studentData.getSchoolCode());
            
            // 处理学校信息 - 通过schoolCode关联
            School school = mongoDBUtils.findOne(
                Criteria.where("schoolCode").is(studentData.getSchoolCode()), 
                School.class
            );
            
            if (school == null) {
                logger.info("Creating new school with code: {}", studentData.getSchoolCode());
                school = new School();
                school.setSchoolCode(studentData.getSchoolCode());
            }
            
            // 设置学校相关信息
            school.setSchoolName(studentData.getSchool());
            
            if (StringUtils.hasText(studentData.getZoneName())) {
                school.setZoneName(studentData.getZoneName());
            }
            
            if (StringUtils.hasText(studentData.getTeacherNumber())) {
                school.setTeacherNumber(studentData.getTeacherNumber());
            }
            
            if (StringUtils.hasText(studentData.getContactNumber())) {
                school.setContactNumber(studentData.getContactNumber());
            }
            
            // 保存/更新学校信息
            mongoDBUtils.save(school);
            logger.info("School information saved/updated for code: {}", studentData.getSchoolCode());
            
            return true;
        } catch (Exception e) {
            logger.error("Error processing school information for student: {}, error: {}", 
                       studentData.getExamCard(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getOrder() {
        // 学校信息应该在基本信息之后处理
        return 2;
    }
} 