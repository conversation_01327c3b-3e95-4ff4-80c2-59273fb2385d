package com.zkdiman.server.service.strategy.export;

import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.entity.ExportTask;

/**
 * 导出上下文类
 * 封装导出过程中需要的所有信息
 */
public class ExportContext {
    
    private final String taskId;
    private final String examId;
    private final String taskType;
    private final Exam exam;
    private final ExportTask exportTask;
    
    public ExportContext(String taskId, String examId, String taskType, Exam exam, ExportTask exportTask) {
        this.taskId = taskId;
        this.examId = examId;
        this.taskType = taskType;
        this.exam = exam;
        this.exportTask = exportTask;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public String getExamId() {
        return examId;
    }
    
    public String getTaskType() {
        return taskType;
    }
    
    public Exam getExam() {
        return exam;
    }
    
    public ExportTask getExportTask() {
        return exportTask;
    }
}
