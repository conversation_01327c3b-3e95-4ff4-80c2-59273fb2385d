package com.zkdiman.server.service.strategy.export;

import com.zkdiman.pojo.entity.Exam;

/**
 * 导出策略接口
 * 使用策略模式来处理不同类型的导出任务
 */
public interface ExportStrategy {
    
    /**
     * 执行导出操作
     * 
     * @param context 导出上下文，包含任务信息和相关数据
     * @return 导出结果，包含文件URL和文件名
     * @throws Exception 导出过程中的异常
     */
    ExportResult execute(ExportContext context) throws Exception;
    
    /**
     * 获取策略支持的任务类型
     * 
     * @return 任务类型标识
     */
    String getSupportedTaskType();
    
    /**
     * 导出结果封装类
     */
    class ExportResult {
        private final String fileUrl;
        private final String fileName;
        
        public ExportResult(String fileUrl, String fileName) {
            this.fileUrl = fileUrl;
            this.fileName = fileName;
        }
        
        public String getFileUrl() {
            return fileUrl;
        }
        
        public String getFileName() {
            return fileName;
        }
    }
}
