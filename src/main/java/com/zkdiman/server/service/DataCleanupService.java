package com.zkdiman.server.service;

/**
 * 数据清理服务接口
 * 负责清理系统中的重复和无效数据
 */
public interface DataCleanupService {
    
    /**
     * 清理重复的答题记录
     * 对于相同的questionId和questionType组合，只保留最新的记录
     * 
     * @return 清理结果统计
     */
    CleanupResult cleanupDuplicateAnswers();
    
    /**
     * 清理指定学生的重复答题记录
     * 
     * @param examCard 准考证号
     * @return 清理结果统计
     */
    CleanupResult cleanupDuplicateAnswersForStudent(String examCard);
    
    /**
     * 验证答题记录的完整性
     * 
     * @return 验证结果
     */
    ValidationResult validateAnswerIntegrity();
    
    /**
     * 清理结果统计类
     */
    class CleanupResult {
        private int totalStudents;           // 总学生数
        private int studentsWithDuplicates; // 有重复记录的学生数
        private int totalDuplicatesRemoved; // 总共移除的重复记录数
        private int totalRecordsAfter;      // 清理后的总记录数
        
        public CleanupResult(int totalStudents, int studentsWithDuplicates, int totalDuplicatesRemoved, int totalRecordsAfter) {
            this.totalStudents = totalStudents;
            this.studentsWithDuplicates = studentsWithDuplicates;
            this.totalDuplicatesRemoved = totalDuplicatesRemoved;
            this.totalRecordsAfter = totalRecordsAfter;
        }
        
        // Getters
        public int getTotalStudents() { return totalStudents; }
        public int getStudentsWithDuplicates() { return studentsWithDuplicates; }
        public int getTotalDuplicatesRemoved() { return totalDuplicatesRemoved; }
        public int getTotalRecordsAfter() { return totalRecordsAfter; }
        
        @Override
        public String toString() {
            return String.format(
                "清理结果: 总学生数=%d, 有重复记录的学生数=%d, 移除重复记录数=%d, 清理后总记录数=%d",
                totalStudents, studentsWithDuplicates, totalDuplicatesRemoved, totalRecordsAfter
            );
        }
    }
    
    /**
     * 验证结果类
     */
    class ValidationResult {
        private int totalStudents;
        private int studentsWithIssues;
        private int totalAnswers;
        private int invalidAnswers;
        
        public ValidationResult(int totalStudents, int studentsWithIssues, int totalAnswers, int invalidAnswers) {
            this.totalStudents = totalStudents;
            this.studentsWithIssues = studentsWithIssues;
            this.totalAnswers = totalAnswers;
            this.invalidAnswers = invalidAnswers;
        }
        
        // Getters
        public int getTotalStudents() { return totalStudents; }
        public int getStudentsWithIssues() { return studentsWithIssues; }
        public int getTotalAnswers() { return totalAnswers; }
        public int getInvalidAnswers() { return invalidAnswers; }
        
        @Override
        public String toString() {
            return String.format(
                "验证结果: 总学生数=%d, 有问题的学生数=%d, 总答题记录数=%d, 无效记录数=%d",
                totalStudents, studentsWithIssues, totalAnswers, invalidAnswers
            );
        }
    }
}
