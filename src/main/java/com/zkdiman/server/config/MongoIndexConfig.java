package com.zkdiman.server.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexInfo;
import javax.annotation.PostConstruct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import java.util.List;

/**
 * MongoDB索引配置类
 * 用于在应用启动时确保所有必要的索引都已创建
 */
@Configuration
public class MongoIndexConfig {
    private static final Logger logger = LogManager.getLogger(MongoIndexConfig.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 在应用启动时创建所有必要的索引
     */
    @PostConstruct
    public void initIndexes() {
        logger.info("开始创建MongoDB索引...");
        
        // 为CompetitionSignUpLog集合创建索引
        createCompetitionSignUpLogIndexes();
        
        // 为CompetitionQuestionLog集合创建索引
        createCompetitionQuestionLogIndexes();
        
        logger.info("MongoDB索引创建完成");
    }

    /**
     * 为学生报名记录表创建索引
     */
    private void createCompetitionSignUpLogIndexes() {
        String collectionName = "CompetitionSignUpLog";
        IndexOperations indexOps = mongoTemplate.indexOps(collectionName);
        List<IndexInfo> existingIndexes = indexOps.getIndexInfo();
        
        // 为examCard字段创建唯一索引，这是最常用的查询字段
        createIndexIfNotExists(existingIndexes, indexOps, "examCard", true, collectionName);

        // 为name字段创建索引，用于按姓名查询
        createIndexIfNotExists(existingIndexes, indexOps, "name", false, collectionName);
        
        // 为school字段创建索引，用于按学校查询
        createIndexIfNotExists(existingIndexes, indexOps, "school", false, collectionName);
        
        // 为examID字段创建索引，用于按考试ID查询
        createIndexIfNotExists(existingIndexes, indexOps, "examID", false, collectionName);

        // 为grade字段创建索引，用于按年级查询
        createIndexIfNotExists(existingIndexes, indexOps, "grade", false, collectionName);
        
        logger.info("CompetitionSignUpLog索引创建完成");
    }

    /**
     * 为学生答题记录表创建索引
     */
    private void createCompetitionQuestionLogIndexes() {
        String collectionName = "CompetitionQuestionLog";
        IndexOperations indexOps = mongoTemplate.indexOps(collectionName);
        List<IndexInfo> existingIndexes = indexOps.getIndexInfo();
        
        // 为examCard字段创建普通索引（之前是唯一索引）
        createIndexIfNotExists(existingIndexes, indexOps, "examCard", false, collectionName);
        
        // 为examId字段创建索引，用于按考试ID查询
        createIndexIfNotExists(existingIndexes, indexOps, "examId", false, collectionName);
        
        logger.info("CompetitionQuestionLog索引创建完成");
    }
    
    /**
     * 检查索引是否存在，不存在则创建
     * @param existingIndexes 已存在的索引列表
     * @param indexOps 索引操作对象
     * @param fieldName 要创建索引的字段名
     * @param isUnique 是否为唯一索引
     * @param collectionName 集合名称
     */
    private void createIndexIfNotExists(List<IndexInfo> existingIndexes, IndexOperations indexOps, 
                                       String fieldName, boolean isUnique, String collectionName) {
        // 检查索引是否已存在
        boolean indexExists = existingIndexes.stream()
                .anyMatch(indexInfo -> indexInfo.getName().startsWith(fieldName + "_"));
        
        if (!indexExists) {
            Index index = new Index().on(fieldName, Sort.Direction.ASC);
            if (isUnique) {
                index.unique();
            }
            indexOps.ensureIndex(index);
            logger.info("为{}集合的{}字段创建{}索引", 
                      collectionName, fieldName, isUnique ? "唯一" : "普通");
        } else {
            logger.info("{}集合的{}字段索引已存在，跳过创建", collectionName, fieldName);
        }
    }
} 