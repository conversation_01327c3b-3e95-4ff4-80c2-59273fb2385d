package com.zkdiman.server.config;

import com.zkdiman.common.exception.UploadException;
import com.zkdiman.common.utils.RateLimiterUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * 接口限流切面
 * 用于控制高并发场景下的API请求频率
 */
@Aspect
@Component
public class RateLimiterAspect {
    private static final Logger logger = LogManager.getLogger(RateLimiterAspect.class);
    
    @Autowired
    private RateLimiterUtils rateLimiterUtils;
    
    // 设置提交答题截图接口的每秒请求数限制
    private static final double SUBMIT_RATE_LIMIT = 50.0;
    
    // 设置获取令牌的超时时间(毫秒)
    private static final long ACQUIRE_TIMEOUT_MS = 200;
    
    /**
     * 定义切点 - 学生提交答题截图接口
     */
    @Pointcut("execution(* com.zkdiman.server.controller.student.StudentController.submit(..))")
    public void submitPointcut() {}
    
    /**
     * 环绕通知 - 学生提交答题截图接口限流
     */
    @Around("submitPointcut()")
    public Object submitRateLimiter(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        String ipAddr = getIpAddr(request);
        
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String examCard = (args.length > 0 && args[0] != null) ? args[0].toString() : "unknown";
        
        // 设置资源名称，基于IP和考生ID的组合限流
        String resourceName = "submit_" + examCard;
        
        // 获取或创建限流器
        rateLimiterUtils.getRateLimiter(resourceName, SUBMIT_RATE_LIMIT);
        
        // 尝试获取令牌
        boolean acquired = rateLimiterUtils.tryAcquire(resourceName, ACQUIRE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        if (!acquired) {
            logger.warn("请求被限流，准考证号: {}, IP: {}", examCard, ipAddr);
            throw new UploadException("提交频率过高，请稍后再试");
        }
        
        // 继续执行原方法
        return joinPoint.proceed();
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
} 