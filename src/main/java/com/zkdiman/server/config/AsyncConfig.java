package com.zkdiman.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * Defines a custom thread pool for student login operations.
     * Configuration parameters are adjusted for higher concurrency.
     * For a 4-core/8GB server, these are more aggressive settings.
     * Monitoring is crucial to fine-tune these further.
     */
    @Bean("studentLoginExecutor")
    public Executor studentLoginExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // For a 4-core system, a core pool size of 8-16 for I/O bound tasks could be reasonable.
        // Max pool size can be larger to handle bursts.
        executor.setCorePoolSize(8); 
        executor.setMaxPoolSize(50); // Significantly increased max pool size
        executor.setQueueCapacity(500); // Significantly increased queue capacity
        executor.setThreadNamePrefix("LoginExec-");
        // Optional: Consider a different rejection policy if AbortPolicy is too harsh.
        // CallerRunsPolicy: Task will be executed by the calling thread (Tomcat thread), which can slow down request handling.
        // DiscardPolicy: Silently discards the task.
        // DiscardOldestPolicy: Discards the oldest task in the queue and tries to execute the current one.
        // For now, we stick to default AbortPolicy to clearly see rejections.
        // executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    // 日志记录线程池配置
    @Bean("logExecutor")
    public Executor logExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("Log-");
        executor.initialize();
        return executor;
    }

    /**
     * 导出任务专用线程池配置
     * 用于处理异步导出任务，避免阻塞其他业务
     */
    @Bean("exportTaskExecutor")
    public Executor exportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 导出任务通常是I/O密集型，可以设置较多的线程
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50); // 限制队列大小，避免内存占用过多
        executor.setThreadNamePrefix("ExportTask-");

        // 设置拒绝策略：当线程池和队列都满时，由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 设置线程空闲时间
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);

        executor.initialize();
        return executor;
    }
}