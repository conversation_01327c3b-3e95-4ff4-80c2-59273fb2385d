package com.zkdiman.server.config;

import com.zkdiman.common.json.JacksonObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.List;

/**
 * Web MVC 配置类 (最终最佳实践版)
 * 使用 @Profile 注解来精确控制不同环境下的 Bean 创建
 */
@Slf4j
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    /**
     * 为【生产环境 (pro)】创建的 Docket Bean。
     * 这个 Bean 只有在 'pro' profile 被激活时才会被创建。
     * 它通过不扫描任何 API (RequestHandlerSelectors.none()) 来达到彻底禁用 Swagger 的目的。
     */
    @Bean
    @Profile("pro")
    public Docket productionDocket() {
        log.warn("生产(pro)环境已激活，Knife4j/Swagger 已禁用。");
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.none()) // 不扫描任何接口
                .paths(PathSelectors.none())           // 不扫描任何路径
                .build();
    }

    /**
     * 为【非生产环境 (例如 dev, test)】创建的 Docket Bean。
     * 这个 Bean 只有在 'pro' profile【没有】被激活时才会被创建。
     * 它会创建完整的、可用的 API 文档。
     */
    @Bean
    @Profile("!pro")
    public Docket developmentDocket() {
        log.info("开发(非pro)环境已激活，正在创建 Knife4j/Swagger 接口文档...");
        ApiInfo apiInfo = new ApiInfoBuilder()
                .title("科学素养项目接口文档 (开发环境)")
                .version("1.0")
                .description("科学素养项目接口文档")
                .build();
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.zkdiman.server.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    /**
     * 扩展 Spring MVC 框架的消息转换器。
     * @param converters 转换器列表
     */
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        log.info("扩展消息转换器...");
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(new JacksonObjectMapper());
        converters.add(0, converter);
    }
}