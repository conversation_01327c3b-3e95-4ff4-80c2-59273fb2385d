package com.zkdiman.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 * 用于处理系统中的异步任务
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    /**
     * 配置核心线程池大小
     */
    private static final int CORE_POOL_SIZE = 10;
    
    /**
     * 配置最大线程池大小
     */
    private static final int MAX_POOL_SIZE = 20;
    
    /**
     * 配置队列容量
     */
    private static final int QUEUE_CAPACITY = 500;
    
    /**
     * 配置线程池中的线程的名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "Async-Service-";

    /**
     * 创建异步任务执行器
     * @return Executor 执行器实例
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);
        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        // 队列容量
        executor.setQueueCapacity(QUEUE_CAPACITY);
        // 线程名前缀
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        // 使用CallerRunsPolicy策略，当线程池满时，由调用线程处理任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 设置线程池中任务的等待时间，如果超过这个时间还没有销毁就强制销毁
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        return executor;
    }
} 