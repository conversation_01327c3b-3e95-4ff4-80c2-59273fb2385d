package com.zkdiman.server.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.util.concurrent.TimeUnit;

@Data
@Configuration
@EnableMongoRepositories(basePackages = "com.zkdiman.server.reposity")
public class MongoConfig {

    @Value("${spring.data.mongodb.host}")
    private String host;

    @Value("${spring.data.mongodb.port}")
    private String port;

    @Value("${spring.data.mongodb.database}")
    private String database;

    @Bean
    public MongoClient mongoClient() {
        String uri = "mongodb://" + host + ":" + port + "/" + database;
        // It's generally better to configure pool size via ConnectionString if possible,
        // but since we are using MongoClientSettings builder, we'll do it there explicitly.
        // Ensure that the URI does not also contain conflicting pool size settings if you use it directly elsewhere.
        ConnectionString connectionString = new ConnectionString(uri);
        
        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(connectionString) // Applies basic settings from URI, but we override pool settings below
                .applyToSocketSettings(builder -> 
                    builder.connectTimeout(10, TimeUnit.SECONDS) // Reduced connect timeout
                           .readTimeout(15, TimeUnit.SECONDS))    // Reduced read timeout
                .applyToConnectionPoolSettings(builder -> 
                    builder.minSize(10) // Minimum number of connections in the pool
                           .maxSize(100) // Maximum number of connections in the pool
                           .maxConnectionIdleTime(60, TimeUnit.SECONDS) // Max idle time
                           .maxConnectionLifeTime(30, TimeUnit.MINUTES) // Max life time for a connection to prevent stale connections
                           .maxWaitTime(10, TimeUnit.SECONDS) // Reduced max wait time for a connection
                           .maintenanceFrequency(1, TimeUnit.MINUTES)
                           .maintenanceInitialDelay(10, TimeUnit.SECONDS))
                .applyToServerSettings(builder -> 
                    builder.heartbeatFrequency(10, TimeUnit.SECONDS) // Standard heartbeat frequency
                           .minHeartbeatFrequency(500, TimeUnit.MILLISECONDS))
                .build();
        
        return MongoClients.create(settings);
    }

    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), database);
    }
} 