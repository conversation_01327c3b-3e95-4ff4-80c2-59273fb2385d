package com.zkdiman.server.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson配置类
 * 配置JSON序列化和反序列化的行为
 */
@Configuration
public class JacksonConfig {
    
    /**
     * 配置ObjectMapper Bean
     * 用于日志记录和其他JSON序列化需求
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 序列化配置
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);           // 忽略空Bean
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);     // 日期不写为时间戳
        mapper.configure(SerializationFeature.WRITE_ENUMS_USING_TO_STRING, true);    // 枚举使用toString
        mapper.configure(SerializationFeature.INDENT_OUTPUT, false);                 // 不缩进输出（节省空间）
        
        // 反序列化配置
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false); // 忽略未知属性
        mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false); // 允许基本类型为null
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true); // 空字符串作为null
        
        // 注册Java 8时间模块
        mapper.registerModule(new JavaTimeModule());
        
        return mapper;
    }
    
    /**
     * 专门用于日志记录的ObjectMapper
     * 配置更加宽松的序列化策略
     */
    @Bean("loggingObjectMapper")
    public ObjectMapper loggingObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 序列化配置 - 更加宽松
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);           // 忽略空Bean
        mapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);       // 忽略自引用
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);     // 日期不写为时间戳
        mapper.configure(SerializationFeature.WRITE_ENUMS_USING_TO_STRING, true);    // 枚举使用toString
        mapper.configure(SerializationFeature.INDENT_OUTPUT, false);                 // 不缩进输出
        
        // 反序列化配置 - 更加宽松
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false); // 忽略未知属性
        mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false); // 允许基本类型为null
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true); // 空字符串作为null
        mapper.configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false);     // 忽略无效子类型
        
        // 注册Java 8时间模块
        mapper.registerModule(new JavaTimeModule());
        
        return mapper;
    }
}
