package com.zkdiman.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 日志配置类
 * 从application.yml读取配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "logging.aop")
public class LoggingConfig {
    
    /**
     * 是否启用AOP日志
     */
    private boolean enabled = true;
    
    /**
     * 需要记录日志的包路径
     */
    private List<String> packages;
    
    /**
     * 需要记录日志的类
     */
    private List<String> classes;
    
    /**
     * 需要记录日志的方法
     */
    private List<String> methods;
    
    /**
     * 排除的包路径
     */
    private List<String> excludePackages;
    
    /**
     * 排除的类
     */
    private List<String> excludeClasses;
    
    /**
     * 排除的方法
     */
    private List<String> excludeMethods;
    
    /**
     * 是否记录请求参数
     */
    private boolean logArgs = true;
    
    /**
     * 是否记录返回结果
     */
    private boolean logResult = true;
    
    /**
     * 是否记录执行时间
     */
    private boolean logExecutionTime = true;
}