package com.zkdiman.server.config;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存配置类
 * 使用本地内存缓存提高高并发场景下的性能
 * 支持缓存大小限制和自动清理
 */
@Configuration
@EnableCaching
public class CacheConfig {

    // 缓存配置常量
    private static final int DEFAULT_CACHE_SIZE = 10000;      // 默认缓存大小
    private static final int LOGIN_CACHE_SIZE = 5000;         // 登录缓存大小
    private static final int EXISTS_CACHE_SIZE = 3000;        // 存在性检查缓存大小
    private static final int EXAM_CACHE_SIZE = 1000;          // 考试缓存大小
    private static final int TASK_CACHE_SIZE = 500;           // 任务缓存大小

    /**
     * 配置缓存管理器
     * 使用SimpleCacheManager配置多个具有大小限制的缓存
     *
     * @return CacheManager 缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        SimpleCacheManager cacheManager = new SimpleCacheManager();

        // 配置各种缓存
        cacheManager.setCaches(Arrays.asList(
            createLimitedCache("studentLoginCache", LOGIN_CACHE_SIZE),     // 学生登录信息缓存
            createLimitedCache("studentExistsCache", EXISTS_CACHE_SIZE),   // 学生存在性检查缓存
            createLimitedCache("examCache", EXAM_CACHE_SIZE),              // 考试信息缓存
            createLimitedCache("taskCache", TASK_CACHE_SIZE),              // 任务信息缓存
            createLimitedCache("schoolCache", DEFAULT_CACHE_SIZE),         // 学校信息缓存
            createLimitedCache("adminCache", DEFAULT_CACHE_SIZE)           // 管理员信息缓存
        ));

        return cacheManager;
    }

    /**
     * 创建具有大小限制的缓存
     *
     * @param name 缓存名称
     * @param maxSize 最大大小
     * @return Cache 缓存实例
     */
    private Cache createLimitedCache(String name, int maxSize) {
        return new ConcurrentMapCache(name, new LimitedConcurrentHashMap<>(maxSize), true);
    }

    /**
     * 具有大小限制的ConcurrentHashMap实现
     * 当达到最大大小时，移除最老的条目
     */
    private static class LimitedConcurrentHashMap<K, V> extends ConcurrentHashMap<K, V> {
        private final int maxSize;

        public LimitedConcurrentHashMap(int maxSize) {
            super();
            this.maxSize = maxSize;
        }

        @Override
        public V put(K key, V value) {
            // 如果达到最大大小，清理一些条目
            if (size() >= maxSize) {
                // 简单策略：清理一半的条目
                int toRemove = maxSize / 2;
                int removed = 0;

                for (K k : keySet()) {
                    if (removed >= toRemove) {
                        break;
                    }
                    remove(k);
                    removed++;
                }
            }

            return super.put(key, value);
        }

        @Override
        public void putAll(java.util.Map<? extends K, ? extends V> m) {
            // 批量添加时也要检查大小
            for (java.util.Map.Entry<? extends K, ? extends V> entry : m.entrySet()) {
                put(entry.getKey(), entry.getValue());
            }
        }
    }
}