package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.PageResult;
import com.zkdiman.common.result.Result;
import com.zkdiman.pojo.dto.admin.AdminLoginDTO;
import com.zkdiman.pojo.dto.admin.AdminQueryDTO;
import com.zkdiman.pojo.dto.admin.AdminSaveDTO;
import com.zkdiman.pojo.dto.admin.AdminDeleteDTO;
import com.zkdiman.pojo.entity.Admin;
import com.zkdiman.pojo.vo.admin.AdminLoginVO;
import com.zkdiman.server.service.AdminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(tags = "管理端管理员信息管理接口")
@RestController
@RequestMapping("/admin") // 管理端功能的接口基础路径
public class AdminManagementController {
    private static final Logger logger = LogManager.getLogger(AdminManagementController.class);

    @Autowired
    private AdminService adminService;

    /**
     * 管理员登录接口
     * @param adminLoginDTO 登录请求体
     * @return 登录结果，包含管理员信息和JWT令牌
     */
    @ApiOperation(value = "管理员登录")
    @PostMapping("/login")
    public Result<AdminLoginVO> login(@RequestBody @Valid AdminLoginDTO adminLoginDTO) {
        AdminLoginVO loginVo = adminService.login(adminLoginDTO.getUsername(), adminLoginDTO.getPassword());
        logger.info("管理员 {} 登录成功", adminLoginDTO.getUsername());
        return Result.success(loginVo);
    }

    /**
     * 管理员列表查询（分页）
     * @param adminQueryDTO 查询条件
     * @return 分页查询结果
     */
    @GetMapping(value = "/admins")
    @ApiOperation("管理员列表查询（分页）")
    public Result<PageResult> getAdminListPage(@Valid AdminQueryDTO adminQueryDTO) {
        logger.info("查询管理员列表，参数: {}", adminQueryDTO);
        PageResult pageResult = adminService.getAdminListPage(adminQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取单个管理员信息
     * @param id 管理员ID
     * @return 管理员详情 (不含密码)
     */
    @GetMapping(value = "/admins/{id}")
    @ApiOperation("根据ID获取单个管理员信息")
    public Result<Admin> getAdminById(@PathVariable String id) {
        logger.info("根据ID查询管理员, ID: {}", id);
        Admin admin = adminService.getAdminById(id);
        return Result.success(admin);
    }

    /**
     * 新增管理员信息
     * @param adminSaveDTO 管理员信息
     * @return 操作结果 (可返回新增的管理员信息，不含密码)
     */
    @PostMapping(value = "/admins")
    @ApiOperation("新增管理员信息")
    public Result<Admin> addAdmin(@RequestBody @Valid AdminSaveDTO adminSaveDTO) {
        Admin createdAdmin = adminService.addAdmin(adminSaveDTO);
        return Result.success(createdAdmin); // 返回创建的管理员信息（不含密码），这样比较安全
    }

    /**
     * 修改管理员信息
     * @param id 管理员ID
     * @param adminSaveDTO 更新的管理员信息
     * @return 操作结果
     */
    @PutMapping(value = "/admins/{id}")
    @ApiOperation("修改管理员信息")
    public Result updateAdmin(@PathVariable String id, @RequestBody @Valid AdminSaveDTO adminSaveDTO) {
        adminService.updateAdmin(id, adminSaveDTO);
        return Result.success("操作成功");
    }

    /**
     * 批量删除管理员
     * @param adminDeleteDTO 包含要删除的管理员ID列表的DTO
     * @return 操作结果
     */
    @DeleteMapping(value = "/admins")
    @ApiOperation("批量删除管理员")
    public Result deleteAdmins(@RequestBody @Valid AdminDeleteDTO adminDeleteDTO) {
        // 注意：要实现自我删除检查，这里需要当前管理员的ID。
         String currentAdminId = adminDeleteDTO.getCurrentAdminId();
         adminService.deleteAdmins(adminDeleteDTO.getAdminIds(), currentAdminId);
        return Result.success("删除成功");
    }
} 