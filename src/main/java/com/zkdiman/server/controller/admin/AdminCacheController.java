package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.Result;
import com.zkdiman.server.service.CacheScheduleService;
import com.zkdiman.server.service.CacheMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 缓存管理控制器
 * 提供缓存管理相关的API接口
 */
@Api(tags = "缓存管理")
@RestController
@RequestMapping("/admin/cache")
public class AdminCacheController {
    
    private static final Logger logger = LogManager.getLogger(AdminCacheController.class);
    
    @Autowired
    private CacheScheduleService cacheScheduleService;

    @Autowired
    private CacheMonitorService cacheMonitorService;
    
    /**
     * 手动清除所有缓存
     */
    @PostMapping("/clear/all")
    @ApiOperation("手动清除所有缓存")
    public Result<String> clearAllCache() {
        logger.info("Controller: 接收到手动清除所有缓存请求");
        
        try {
            cacheScheduleService.manualClearAllCache();
            logger.info("Controller: 手动清除所有缓存成功");
            return Result.success("所有缓存已清除");
        } catch (Exception e) {
            logger.error("Controller: 手动清除所有缓存失败: {}", e.getMessage(), e);
            return Result.error("清除缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取缓存统计信息")
    public Result<String> getCacheStatistics() {
        logger.info("Controller: 接收到获取缓存统计信息请求");

        try {
            CacheMonitorService.CacheStatistics statistics = cacheMonitorService.getCacheStatistics();
            logger.info("Controller: 获取缓存统计信息成功");
            return Result.success(statistics.toString());
        } catch (Exception e) {
            logger.error("Controller: 获取缓存统计信息失败: {}", e.getMessage(), e);
            return Result.error("获取缓存统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发每日缓存清理任务
     */
    @PostMapping("/clear/daily")
    @ApiOperation("手动触发每日缓存清理任务")
    public Result<String> triggerDailyCacheClear() {
        logger.info("Controller: 接收到手动触发每日缓存清理请求");

        try {
            cacheScheduleService.clearStudentCacheDaily();
            logger.info("Controller: 手动触发每日缓存清理成功");
            return Result.success("每日缓存清理任务已执行");
        } catch (Exception e) {
            logger.error("Controller: 手动触发每日缓存清理失败: {}", e.getMessage(), e);
            return Result.error("触发每日缓存清理失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发缓存大小检查
     */
    @PostMapping("/check/size")
    @ApiOperation("手动触发缓存大小检查")
    public Result<String> checkCacheSize() {
        logger.info("Controller: 接收到手动触发缓存大小检查请求");

        try {
            cacheMonitorService.manualCheckCacheSize();
            logger.info("Controller: 手动触发缓存大小检查成功");
            return Result.success("缓存大小检查已执行");
        } catch (Exception e) {
            logger.error("Controller: 手动触发缓存大小检查失败: {}", e.getMessage(), e);
            return Result.error("触发缓存大小检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取详细的缓存监控信息
     */
    @GetMapping("/monitor")
    @ApiOperation("获取详细的缓存监控信息")
    public Result<CacheMonitorService.CacheStatistics> getCacheMonitorInfo() {
        logger.info("Controller: 接收到获取缓存监控信息请求");

        try {
            CacheMonitorService.CacheStatistics statistics = cacheMonitorService.getCacheStatistics();
            logger.info("Controller: 获取缓存监控信息成功");
            return Result.success(statistics);
        } catch (Exception e) {
            logger.error("Controller: 获取缓存监控信息失败: {}", e.getMessage(), e);
            return Result.error("获取缓存监控信息失败: " + e.getMessage());
        }
    }
}
