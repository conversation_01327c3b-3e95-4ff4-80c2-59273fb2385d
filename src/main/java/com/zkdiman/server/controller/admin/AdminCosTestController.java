package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.Result;
import com.zkdiman.common.utils.CosConnectionTest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 腾讯云COS测试控制器
 * 用于测试和验证COS配置
 */
@Api(tags = "腾讯云COS测试接口")
@RestController
@RequestMapping("/admin/cos")
public class AdminCosTestController {
    
    private static final Logger logger = LogManager.getLogger(AdminCosTestController.class);
    
    @Autowired
    private CosConnectionTest cosConnectionTest;
    
    /**
     * 测试COS连接
     */
    @GetMapping("/test")
    @ApiOperation("测试COS连接并列出存储桶")
    public Result<String> testCosConnection() {
        logger.info("收到COS连接测试请求");
        try {
            cosConnectionTest.testConnection();
            return Result.success("COS连接测试完成，请查看日志获取详细信息");
        } catch (Exception e) {
            logger.error("COS连接测试失败: {}", e.getMessage(), e);
            return Result.error("COS连接测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试指定地域的COS连接
     */
    @GetMapping("/test/{region}")
    @ApiOperation("测试指定地域的COS连接")
    public Result<String> testCosConnectionWithRegion(
            @ApiParam(value = "地域标识", required = true) @PathVariable String region) {
        logger.info("收到地域 {} 的COS连接测试请求", region);
        try {
            cosConnectionTest.testConnectionWithRegion(region);
            return Result.success("地域 " + region + " 的COS连接测试完成，请查看日志获取详细信息");
        } catch (Exception e) {
            logger.error("地域 {} 的COS连接测试失败: {}", region, e.getMessage(), e);
            return Result.error("地域 " + region + " 的COS连接测试失败: " + e.getMessage());
        }
    }
}
