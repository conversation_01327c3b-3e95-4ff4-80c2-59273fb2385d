package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.PageResult;
import com.zkdiman.common.result.Result;
import com.zkdiman.pojo.dto.exam.ExamQueryDTO;
import com.zkdiman.pojo.dto.exam.ExamAddDTO;
import com.zkdiman.pojo.dto.exam.ExamUpdateDTO;
import com.zkdiman.pojo.dto.exam.ExamStudentQueryDTO;
import com.zkdiman.pojo.dto.exam.ExamAddStudentsDTO;
import com.zkdiman.pojo.dto.exam.ExamRemoveStudentsDTO;
import com.zkdiman.pojo.dto.student.StudentTimeAdjustmentDTO;
import com.zkdiman.pojo.entity.Exam;
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import com.zkdiman.server.service.ExamService;
import com.zkdiman.server.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "管理端考试场次接口")
@RestController
@RequestMapping("/admin")
public class AdminExamController {
    private static final Logger logger = LogManager.getLogger(AdminExamController.class);

    @Autowired
    private ExamService examService;

    @Autowired
    private StudentService studentService;

    /**
     * 新增考试场次
     * @param examAddDTO 考试场次信息
     * @return 操作结果
     */
    @PostMapping(value = "/exams")
    @ApiOperation("新增考试场次")
    public Result addExam(@RequestBody ExamAddDTO examAddDTO) {
        examService.addExam(examAddDTO);
        return Result.success();
    }

    /**
     * 根据ID查询考试场次
     * @param id 考试场次ID
     * @return 考试场次详情
     */
    @GetMapping(value = "/exams/{id}")
    @ApiOperation("根据ID查询考试场次")
    public Result<Exam> getExamById(@PathVariable String id) {
        Exam exam = examService.getExamById(id);
        if (exam == null) {
            return Result.error("考试场次不存在"); // 或者根据具体错误码规范调整
        }
        return Result.success(exam);
    }

    /**
     * 修改考试场次
     * @param id 考试场次ID
     * @param examUpdateDTO 更新的考试信息
     * @return 操作结果
     */
    @PutMapping(value = "/exams/{id}")
    @ApiOperation("修改考试场次")
    public Result updateExam(@PathVariable String id, @RequestBody ExamUpdateDTO examUpdateDTO) {
        examService.updateExam(id, examUpdateDTO);
        // 清除学生相关缓存
        studentService.clearAllStudentCache();
        return Result.success("修改成功");
    }

    /**
     * 考试场次列表查询（分页）
     * @param examQueryDTO 查询条件
     * @return 分页结果
     */
    @GetMapping(value = "/exams")
    @ApiOperation("考试场次列表查询（分页）")
    public Result<PageResult> getExamListPage(ExamQueryDTO examQueryDTO) {
        logger.info("查询考试场次列表，参数: {}", examQueryDTO);
        PageResult pageResult = examService.getExamListPage(examQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 删除考试场次
     * @param id 考试场次ID
     * @return 操作结果
     */
    @DeleteMapping(value = "/exams/{id}")
    @ApiOperation("删除考试场次")
    public Result deleteExam(@PathVariable String id) {
        // 删除考试场次
        examService.deleteExamById(id);
        // 清除学生相关缓存
        studentService.clearAllStudentCache();
        return Result.success("删除成功");
    }

    /**
     * 查询指定考试场次下的学员列表（分页）
     * @param id 考试场次ID (Exam表的ID)
     * @param examStudentQueryDTO 查询参数
     * @return 分页的学员列表
     */
    @GetMapping(value = "/exams/students/{id}") // Path matches the requirement: /admin/exams/students/{id}
    @ApiOperation("查询场次下的学员列表")
    public Result<PageResult> getStudentsForExam(@PathVariable String id, ExamStudentQueryDTO examStudentQueryDTO) {
        PageResult pageResult = examService.getStudentsByExamId(id, examStudentQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 为指定考试场次批量添加学员
     * @param id 考试场次ID (Exam表的ID)
     * @param examAddStudentsDTO 包含学生ID列表的DTO
     * @return 操作结果
     */
    @PostMapping(value = "/exams/students/{id}")
    @ApiOperation("为场次批量添加学员")
    public Result addStudentsToExam(@PathVariable String id, @RequestBody @Valid ExamAddStudentsDTO examAddStudentsDTO) {
        examService.addStudentsToExam(id, examAddStudentsDTO);
        return Result.success("添加成功");
    }

    /**
     * 从指定考试场次批量移除学员
     * @param id 考试场次ID (Exam表的ID)
     * @param examRemoveStudentsDTO 包含学生ID列表的DTO
     * @return 操作结果
     */
    @DeleteMapping(value = "/exams/students/{id}")
    @ApiOperation("从场次批量移除学员")
    public Result removeStudentsFromExam(@PathVariable String id, @RequestBody @Valid ExamRemoveStudentsDTO examRemoveStudentsDTO) {
        // 执行移除操作
        examService.removeStudentsFromExam(id, examRemoveStudentsDTO.getStudentIds());
        // 清除学生相关缓存
        studentService.clearAllStudentCache();
        return Result.success("移除成功");
    }

    /**
     * 为单个学生调整考试时间
     * @param examId 考试场次ID
     * @param studentId 学生在本次考试中的记录ID (CompetitionSignUpLog ID)
     * @param studentTimeAdjustmentDTO 调整的时间信息
     * @return 操作结果
     */
    @PutMapping(value = "/exams/{examId}/students/{studentId}/time-adjustment")
    @ApiOperation("为单个学生调整考试时间")
    public Result adjustStudentExamTime(
            @PathVariable String examId,
            @PathVariable String studentId,
            @RequestBody @Valid StudentTimeAdjustmentDTO studentTimeAdjustmentDTO) {
        if (studentTimeAdjustmentDTO.getStartTime() == null && studentTimeAdjustmentDTO.getDuration() == null) {
            return Result.error("至少需要提供 startTime 或 duration 中的一个参数进行调整");
        }

        // 清除学生相关缓存
        studentService.clearAllStudentCache();
        examService.adjustStudentExamTime(examId, studentId, studentTimeAdjustmentDTO);
        return Result.success("学生考试时间调整成功");
    }

    /**
     * 通过Excel批量导入学员到场次并分配考试时间
     * @param examId 考试场次ID
     * @param file Excel文件
     * @param startTime 统一考试开始时间 (Epoch毫秒)
     * @param duration 统一考试时长 (分钟)
     * @return 导入结果
     */
    @PostMapping(value = "/exams/{examId}/students/batch-import-assigned", consumes = "multipart/form-data")
    @ApiOperation("通过Excel批量导入学员到场次并分配考试时间")
    public Result<BatchImportResultVO> batchImportAssignedStudents(
            @PathVariable String examId,
            @RequestParam("file") MultipartFile file,
            @RequestParam("startTime") Long startTime,
            @RequestParam("duration") Integer duration) {
        logger.info("Controller: 批量导入学员到考试场次 {}，文件: {}, startTime: {}, duration: {}",
                examId, file.getOriginalFilename(), startTime, duration);

        if (file.isEmpty()) {
            return Result.error("上传的Excel文件不能为空");
        }
        if (startTime == null || duration == null) {
            return Result.error("必须提供startTime和duration参数");
        }
        if (duration <=0) {
            return Result.error("考试时长必须大于0");
        }

        BatchImportResultVO resultVO = examService.batchImportAssignedStudents(examId, file, startTime, duration);

        if (resultVO.getFailureCount() == 0 && resultVO.getSuccessCount() > 0) {
            // 全部成功
            return Result.success(resultVO, "批量导入并分配成功");
        } else if (resultVO.getSuccessCount() > 0 && resultVO.getFailureCount() > 0) {
            // 部分成功
            return Result.success(resultVO, "部分学员导入并分配成功");
        } else if (resultVO.getSuccessCount() == 0 && resultVO.getFailureCount() > 0){
            // 全部失败
            // 返回Result.success以便在data字段中携带resultVO，通过msg和前端检查resultVO.failureCount来判断
            // 或者，如果Result类有专门的业务失败码设置方法，可以使用那个。
            // 为简单起见，这里仍然用success封装，前端根据failureCount和successCount判断最终状态码。
            // 另一种选择是 Result.error("批量导入全部失败，详情见数据", resultVO) - 如果Result支持
            // 但根据之前的Result.error(String)模式，我们这样做：
             return Result.success(resultVO, "批量导入处理完成，但存在失败记录。请查看详情。");
        } else { // Excel中没有可处理的数据，或者全部数据都有预检错误（如文件类型不对，这种情况应更早捕获）
             return Result.success(resultVO, "未从Excel文件中处理任何学生数据。");
        }
    }

}
