package com.zkdiman.server.controller.admin;


import com.zkdiman.common.result.Result;
import com.zkdiman.pojo.vo.admin.ExportTaskStatusVO;
import com.zkdiman.server.service.ExamExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "管理端导出考试截图接口")
@RestController
@RequestMapping("/admin")
public class AdminExportScreenshotsController {

    private static final Logger logger = LogManager.getLogger(AdminExportScreenshotsController.class);

    @Autowired
    private ExamExportService examExportService;

    /**
     * 启动异步导出指定考试场次下所有学员的所有答题截图的任务
     * @param id 考试场次ID
     * @return 操作结果，包含任务ID
     */
    @PostMapping("/exams/{id}/export/screenshots/initiate") // 使用POST启动任务更符合RESTful风格
    @ApiOperation("启动异步导出场次学员截图任务 (返回任务ID)")
    public Result<String> initiateExportExamScreenshots(
            @ApiParam(value = "考试场次ID", required = true) @PathVariable String id) {
        logger.info("Controller: 请求启动考试场次 {} 的截图导出任务", id);
        try {
            String taskId = examExportService.initiateExamScreenshotsExport(id);
            logger.info("Controller: 考试场次 {} 的截图导出任务已启动，任务ID: {}", id, taskId);
            return Result.success(taskId, "截图导出任务已启动，任务ID: " + taskId);
        } catch (Exception e) {
            logger.error("Controller: 启动考试场次 {} 的截图导出任务失败: {}", id, e.getMessage(), e);
            return Result.error("启动截图导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询异步导出截图任务的状态
     * @param taskId 任务ID
     * @return 任务状态及结果，完成后包含下载链接
     */
    @GetMapping("/exams/export/screenshots/status/{taskId}")
    @ApiOperation("查询截图导出任务状态 (通过任务ID)")
    public Result<ExportTaskStatusVO> getExportExamScreenshotsStatus(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        logger.info("Controller: 查询截图导出任务状态，任务ID: {}", taskId);
        try {
            ExportTaskStatusVO statusVO = examExportService.getExportTaskStatus(taskId);
            return Result.success(statusVO);
        } catch (Exception e) {
            logger.error("Controller: 查询截图导出任务 {} 状态失败: {}", taskId, e.getMessage(), e);
            return Result.error("查询任务状态失败: " + e.getMessage());
        }
    }
}
