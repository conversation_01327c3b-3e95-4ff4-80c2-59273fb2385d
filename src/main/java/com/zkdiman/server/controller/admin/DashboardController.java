package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.Result;
import com.zkdiman.pojo.vo.admin.DashboardSummaryVO;
import com.zkdiman.server.service.DashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "管理端仪表盘接口")
@RestController
@RequestMapping("/admin/dashboard")
public class DashboardController {

    private static final Logger logger = LogManager.getLogger(DashboardController.class);

    @Autowired
    private DashboardService dashboardService;

    /**
     * 获取仪表盘概要统计
     * @return 概要统计数据
     */
    @GetMapping("/summary")
    @ApiOperation("获取仪表盘概要统计")
    public Result<DashboardSummaryVO> getDashboardSummary() {
        DashboardSummaryVO summary = dashboardService.getDashboardSummary();
        return Result.success(summary);
    }
} 