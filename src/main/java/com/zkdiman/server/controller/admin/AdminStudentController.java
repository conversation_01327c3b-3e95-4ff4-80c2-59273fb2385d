package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.PageResult;
import com.zkdiman.common.result.Result;
import com.zkdiman.pojo.dto.student.StudentPageDTO;
import com.zkdiman.pojo.dto.student.SelectableStudentsQueryDTO;
import com.zkdiman.pojo.entity.Answer;
import com.zkdiman.pojo.vo.admin.BatchImportResultVO;
import com.zkdiman.pojo.vo.student.StudentQueryVO;
import com.zkdiman.server.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;

@Api(tags = "管理端学生管理相关接口")
@RestController
@RequestMapping("/admin")
public class AdminStudentController {
    private static final Logger logger = LogManager.getLogger(AdminStudentController.class);

    @Autowired
    private StudentService studentService;

    /**
     * 学生列表分页查询
     * @param studentPageDto 学生列表查询条件
     * @return 分页查询结果
     */
    @GetMapping(value = "/students")
    @ApiOperation("学生列表分页查询")
    public Result getStudentList(StudentPageDTO studentPageDto) {
        PageResult pageResult = studentService.getStudentListPage(studentPageDto);
        return Result.success(pageResult);
    }

    /**
     * 获取单个学生信息(用于编辑)
     * @param id 学生id
     * @return 单个学生信息
     */
    @GetMapping(value = "/students/{id}")
    @ApiOperation("获取单个学生信息(用于编辑)")
    public Result getStudentById(@PathVariable("id") String id) {
         StudentQueryVO studentQueryVO=studentService.getStudentById(id);
        return Result.success(studentQueryVO);
    }

    /**
     * 新增学生
     * @param studentQueryVO 学生信息
     * @return 成功
     */
    @PostMapping(value = "/students")
    @ApiOperation("新增学生")
    public Result addStudent(@RequestBody StudentQueryVO studentQueryVO){
        studentService.addOrUpdateStudent(studentQueryVO);
        return Result.success("新增学生成功");
    }

    /**
     * 修改学生信息
     * @param studentQueryVO 学生信息
     * @return 成功
     */
    @PutMapping(value = "/students")
    @ApiOperation("修改学生信息")
    public Result editStudent(@RequestBody StudentQueryVO studentQueryVO){
        studentService.addOrUpdateStudent(studentQueryVO);
        return Result.success("成功修改学生信息");
    }

    /**
     * 删除学生
     * @param id 学生id
     * @return 成功
     */
    @DeleteMapping(value = "/students/{id}")
    @ApiOperation("删除学生")
    public Result deleteStudent(@PathVariable("id") String id){
        studentService.deleteStudent(id);
        return Result.success();
    }

    /**
     * 批量导入学生账号
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping(value = "/students/batch-import")
    @ApiOperation("批量导入学生账号")
    public Result<BatchImportResultVO> batchImportStudents(
            @ApiParam(value = "包含学生信息的Excel文件", required = true)
            @RequestPart("file") MultipartFile file) {
        BatchImportResultVO importResult = studentService.batchImportStudents(file);
        
        String message = "导入处理完成.";
        if (importResult.getFailureCount() > 0 && importResult.getSuccessCount() > 0) {
            message = "部分导入成功.";
        }
        else if (importResult.getFailureCount() > 0 && importResult.getSuccessCount() == 0) {
            message = "导入失败.";
        }
        else if (importResult.getFailureCount() == 0 && importResult.getSuccessCount() > 0) {
            message = "全部导入成功.";
        } else if (importResult.getSuccessCount() == 0 && importResult.getFailureCount() == 0) {
             message = "未导入任何数据或文件内容为空."; // More specific for no data processed
        }
        // Corrected order: data first, then message
        return Result.success(importResult, message);
    }

    /**
     * 查询可被添加到指定考试场次的学员列表
     * @param queryDTO 查询参数
     * @return 分页的学员列表
     */
    @GetMapping("/students/selectable-for-exam")
    @ApiOperation("查询可选学员列表（用于添加到场次）")
    public Result<PageResult> getSelectableStudents(SelectableStudentsQueryDTO queryDTO) {
        logger.info("查询可选学员列表, 参数: {}", queryDTO);
        PageResult pageResult = studentService.getSelectableStudents(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查看单个学生的答题截图
     * @param examCard 学生准考证号
     * @return 截图URL列表
     */
    @GetMapping("/students/{examCard}/screenshots")
    @ApiOperation("查看单个学生的答题截图")
    public Result<ArrayList<Answer>> getStudentScreenshots(
            @ApiParam(value = "学生准考证号", required = true)
            @PathVariable String examCard) {
        ArrayList<Answer> answers = studentService.getScreenshotsByExamCard(examCard);
        if (answers.isEmpty()) {
            return Result.success(answers, "未查询到截图记录");
        }
        return Result.success(answers, "查询成功");
    }
}
