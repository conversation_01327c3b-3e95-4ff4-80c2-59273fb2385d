package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.Result;
import com.zkdiman.server.service.DataCleanupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据清理管理控制器
 * 提供数据清理相关的管理接口
 */
@Api(tags = "数据清理管理")
@RestController
@RequestMapping("/admin/data-cleanup")
public class AdminDataCleanupController {
    
    private static final Logger logger = LogManager.getLogger(AdminDataCleanupController.class);
    
    @Autowired
    private DataCleanupService dataCleanupService;
    
    /**
     * 清理所有学生的重复答题记录
     */
    @PostMapping("/answers/duplicates/all")
    @ApiOperation("清理所有学生的重复答题记录")
    public Result<DataCleanupService.CleanupResult> cleanupAllDuplicateAnswers() {
        logger.info("Controller: 接收到清理所有重复答题记录请求");
        
        try {
            DataCleanupService.CleanupResult result = dataCleanupService.cleanupDuplicateAnswers();
            logger.info("Controller: 清理所有重复答题记录成功: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            logger.error("Controller: 清理所有重复答题记录失败: {}", e.getMessage(), e);
            return Result.error("清理重复答题记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理指定学生的重复答题记录
     */
    @PostMapping("/answers/duplicates/student/{examCard}")
    @ApiOperation("清理指定学生的重复答题记录")
    public Result<DataCleanupService.CleanupResult> cleanupStudentDuplicateAnswers(
            @ApiParam(value = "准考证号", required = true) @PathVariable String examCard) {
        logger.info("Controller: 接收到清理学生 {} 重复答题记录请求", examCard);
        
        try {
            DataCleanupService.CleanupResult result = dataCleanupService.cleanupDuplicateAnswersForStudent(examCard);
            logger.info("Controller: 清理学生 {} 重复答题记录成功: {}", examCard, result);
            return Result.success(result);
        } catch (Exception e) {
            logger.error("Controller: 清理学生 {} 重复答题记录失败: {}", examCard, e.getMessage(), e);
            return Result.error("清理学生重复答题记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证答题记录完整性
     */
    @GetMapping("/answers/validate")
    @ApiOperation("验证答题记录完整性")
    public Result<DataCleanupService.ValidationResult> validateAnswerIntegrity() {
        logger.info("Controller: 接收到验证答题记录完整性请求");
        
        try {
            DataCleanupService.ValidationResult result = dataCleanupService.validateAnswerIntegrity();
            logger.info("Controller: 验证答题记录完整性成功: {}", result);
            return Result.success(result);
        } catch (Exception e) {
            logger.error("Controller: 验证答题记录完整性失败: {}", e.getMessage(), e);
            return Result.error("验证答题记录完整性失败: " + e.getMessage());
        }
    }
}
