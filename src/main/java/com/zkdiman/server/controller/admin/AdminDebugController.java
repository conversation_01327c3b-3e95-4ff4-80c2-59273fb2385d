package com.zkdiman.server.controller.admin;

import com.zkdiman.common.result.Result;
import com.zkdiman.common.utils.MongoDBUtils;
import com.zkdiman.pojo.entity.CompetitionQuestionLog;
import com.zkdiman.pojo.entity.CompetitionSignUpLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器
 * 用于调试数据查询问题
 */
@Api(tags = "调试接口")
@RestController
@RequestMapping("/admin/debug")
public class AdminDebugController {
    
    private static final Logger logger = LogManager.getLogger(AdminDebugController.class);
    
    @Autowired
    private MongoDBUtils mongoDBUtils;
    
    /**
     * 调试学员答题记录查询
     */
    @GetMapping("/question-logs/{examId}")
    @ApiOperation("调试学员答题记录查询")
    public Result<Map<String, Object>> debugQuestionLogs(
            @ApiParam(value = "考试ID", required = true) @PathVariable String examId) {
        
        logger.info("开始调试考试 {} 的答题记录查询", examId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 查询该考试的所有学员
            List<CompetitionSignUpLog> students = mongoDBUtils.find(
                Criteria.where("examID").is(examId), 
                CompetitionSignUpLog.class
            );
            
            logger.info("考试 {} 共有 {} 名学员", examId, students.size());
            result.put("totalStudents", students.size());
            result.put("students", students);
            
            // 2. 查询该考试的所有答题记录
            List<CompetitionQuestionLog> allQuestionLogs = mongoDBUtils.find(
                Criteria.where("ExamId").is(examId), 
                CompetitionQuestionLog.class
            );
            
            logger.info("考试 {} 共有 {} 条答题记录", examId, allQuestionLogs.size());
            result.put("totalQuestionLogs", allQuestionLogs.size());
            result.put("allQuestionLogs", allQuestionLogs);
            
            // 3. 为每个学员查询其答题记录
            Map<String, Object> studentQuestionLogs = new HashMap<>();
            for (CompetitionSignUpLog student : students) {
                List<CompetitionQuestionLog> studentLogs = mongoDBUtils.find(
                    Criteria.where("ExamId").is(examId).and("examCard").is(student.getExamCard()),
                    CompetitionQuestionLog.class
                );
                
                Map<String, Object> studentInfo = new HashMap<>();
                studentInfo.put("name", student.getName());
                studentInfo.put("examCard", student.getExamCard());
                studentInfo.put("questionLogCount", studentLogs.size());
                studentInfo.put("questionLogs", studentLogs);
                
                // 统计该学员的答题记录中的URL数量
                int totalAnswers = 0;
                int validUrls = 0;
                for (CompetitionQuestionLog log : studentLogs) {
                    if (log.getStudentAnswer() != null) {
                        totalAnswers += log.getStudentAnswer().size();
                        validUrls += (int) log.getStudentAnswer().stream()
                                .filter(answer -> answer.getUrl() != null && !answer.getUrl().trim().isEmpty())
                                .count();
                    }
                }
                studentInfo.put("totalAnswers", totalAnswers);
                studentInfo.put("validUrls", validUrls);
                
                studentQuestionLogs.put(student.getExamCard(), studentInfo);
                
                logger.info("学员 {} (准考证号: {}) 有 {} 条答题记录，共 {} 个答案，其中 {} 个有效URL", 
                           student.getName(), student.getExamCard(), studentLogs.size(), totalAnswers, validUrls);
            }
            
            result.put("studentQuestionLogs", studentQuestionLogs);
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("调试查询失败: {}", e.getMessage(), e);
            return Result.error("调试查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询特定学员的答题记录
     */
    @GetMapping("/student-logs/{examId}/{examCard}")
    @ApiOperation("查询特定学员的答题记录")
    public Result<Map<String, Object>> debugStudentLogs(
            @ApiParam(value = "考试ID", required = true) @PathVariable String examId,
            @ApiParam(value = "准考证号", required = true) @PathVariable String examCard) {
        
        logger.info("查询学员 {} 在考试 {} 中的答题记录", examCard, examId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询学员信息
            CompetitionSignUpLog student = mongoDBUtils.findOne(
                Criteria.where("examID").is(examId).and("examCard").is(examCard),
                CompetitionSignUpLog.class
            );
            
            if (student == null) {
                return Result.error("未找到该学员的报名记录");
            }
            
            result.put("student", student);
            
            // 查询答题记录
            List<CompetitionQuestionLog> questionLogs = mongoDBUtils.find(
                Criteria.where("ExamId").is(examId).and("examCard").is(examCard),
                CompetitionQuestionLog.class
            );
            
            result.put("questionLogs", questionLogs);
            result.put("questionLogCount", questionLogs.size());
            
            // 统计答案信息
            int totalAnswers = 0;
            int validUrls = 0;
            for (CompetitionQuestionLog log : questionLogs) {
                if (log.getStudentAnswer() != null) {
                    totalAnswers += log.getStudentAnswer().size();
                    validUrls += (int) log.getStudentAnswer().stream()
                            .filter(answer -> answer.getUrl() != null && !answer.getUrl().trim().isEmpty())
                            .count();
                }
            }
            
            result.put("totalAnswers", totalAnswers);
            result.put("validUrls", validUrls);
            
            logger.info("学员 {} 有 {} 条答题记录，共 {} 个答案，其中 {} 个有效URL", 
                       examCard, questionLogs.size(), totalAnswers, validUrls);
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("查询学员答题记录失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
