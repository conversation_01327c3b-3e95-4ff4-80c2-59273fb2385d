package com.zkdiman.server.controller.student;

import com.zkdiman.common.result.Result;
import com.zkdiman.pojo.dto.student.StudentCompletedDTO;
import com.zkdiman.pojo.dto.student.StudentLoginDTO;
import com.zkdiman.pojo.dto.student.StudentScreenshotSubmitDTO;
import com.zkdiman.pojo.vo.student.StudentLoginVo;
import com.zkdiman.server.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import com.zkdiman.common.exception.BusinessException;

@Api(tags = "学生相关接口")
@RestController
@RequestMapping("/student")
@Validated
public class StudentController {
    private static final Logger logger = LogManager.getLogger(StudentController.class);

    @Autowired
    private StudentService studentService;

    @ApiOperation(value = "学生登录")
    @PostMapping("/login")
    public Result<StudentLoginVo> login(@Valid @RequestBody StudentLoginDTO loginDTO) {
        StudentLoginVo studentLoginVo = studentService.login(loginDTO.getExamCard());

        if (studentLoginVo != null) {
            return Result.success(studentLoginVo, "登录成功");
        } else {
            logger.warn("学生 {} 登录失败: 准考证错误或登录服务失败", loginDTO.getExamCard());
            return Result.error("准考证错误或登录失败");
        }
    }

    /**
     * 根据准考证号提交答题截图URL,题号，分数（主观题）
     * @param submitDTO 包含准考证号和截图URL的DTO
     */
    @ApiOperation(value = "提交答题截图URL,题号，分数（主观题）")
    @PostMapping("/submit-screenshot-url")
    public Result<Void> submitScreenshotUrl(@Valid @RequestBody StudentScreenshotSubmitDTO submitDTO) {
        try {
            CompletableFuture<Void> future = studentService.submitScreenshotUrl(submitDTO);
            future.get(); // Wait for completion
            return Result.success("提交答题截图URL,题号，分数（主观题）成功");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("学生 {} 提交答题数据线程被中断: {}", submitDTO.getExamCard(), e.getMessage());
            return Result.error("提交服务中断，请稍后重试");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof BusinessException) {
                // 重新抛出BusinessException，让全局异常处理器处理
                throw (BusinessException) cause;
            } else if (cause instanceof RuntimeException) {
                // 重新抛出其他运行时异常
                throw (RuntimeException) cause;
            } else {
                logger.error("学生 {} 提交答题数据执行异常: {}", submitDTO.getExamCard(), cause != null ? cause.getMessage() : e.getMessage(), cause);
                return Result.error("提交服务异常，请稍后重试");
            }
        } catch (Exception e) {
            logger.error("学生 {} 提交答题数据时发生未知错误: {}", submitDTO.getExamCard(), e.getMessage(), e);
            return Result.error("提交时发生未知错误，请联系管理员");
        }
    }

    @ApiOperation(value = "学生交卷")
    @PostMapping("/completed")
    public Result<Void> completed(@Valid @RequestBody StudentCompletedDTO completedDTO) {
        logger.info("接收到学生 {} 交卷请求, 类型: {}", completedDTO.getExamCard(), completedDTO.getSubmitType());

        try {
            CompletableFuture<String> future = studentService.completed(completedDTO.getExamCard(), completedDTO.getSubmitType());
            String studentName = future.get();
            String submitDescription = completedDTO.getSubmitType() == 1 ? "主动提交考试" : "时间用尽自动提交考试";
            String message = "学生 " + (studentName != null && !studentName.isEmpty() ? studentName : completedDTO.getExamCard()) + " 已完成考试，" + submitDescription;
            logger.info(message);
            return Result.success(message);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("学生 {} 交卷线程被中断: {}", completedDTO.getExamCard(), e.getMessage());
            return Result.error("交卷服务中断，请稍后重试");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof BusinessException) {
                // 重新抛出BusinessException，让全局异常处理器处理
                throw (BusinessException) cause;
            } else if (cause instanceof RuntimeException) {
                // 重新抛出其他运行时异常
                throw (RuntimeException) cause;
            } else {
                logger.error("学生 {} 交卷执行异常: {}", completedDTO.getExamCard(), cause != null ? cause.getMessage() : e.getMessage(), cause);
                return Result.error("交卷服务异常，请稍后重试");
            }
        } catch (Exception e) {
            logger.error("学生 {} 交卷时发生未知错误: {}", completedDTO.getExamCard(), e.getMessage(), e);
            return Result.error("交卷时发生未知错误，请联系管理员");
        }
    }
} 