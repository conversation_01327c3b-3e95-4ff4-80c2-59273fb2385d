package com.zkdiman.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zkdiman.server.config.JacksonConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LoggingAspect序列化功能测试
 * 验证修复后的序列化机制能够安全处理各种类型的对象
 */
@ExtendWith(MockitoExtension.class)
class LoggingAspectSerializationTest {
    
    private ObjectMapper loggingObjectMapper;
    private LoggingAspect loggingAspect;
    
    @BeforeEach
    void setUp() {
        // 使用修复后的Jackson配置
        JacksonConfig jacksonConfig = new JacksonConfig();
        loggingObjectMapper = jacksonConfig.loggingObjectMapper();
        
        // 创建LoggingAspect实例用于测试私有方法
        loggingAspect = new LoggingAspect();
    }
    
    @Test
    void testSafeSerializeNormalObject() throws Exception {
        // 测试正常对象的序列化
        TestObject testObj = new TestObject("test", 123);
        
        String result = loggingObjectMapper.writeValueAsString(testObj);
        
        assertNotNull(result);
        assertTrue(result.contains("test"));
        assertTrue(result.contains("123"));
    }
    
    @Test
    void testSafeSerializeMultipartFile() throws Exception {
        // 测试MultipartFile的处理
        MultipartFile file = new MockMultipartFile(
            "test", 
            "test.xlsx", 
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
            "test content".getBytes()
        );
        
        // 使用反射调用私有方法sanitizeArgument
        Method sanitizeMethod = LoggingAspect.class.getDeclaredMethod("sanitizeArgument", Object.class);
        sanitizeMethod.setAccessible(true);
        
        Object result = sanitizeMethod.invoke(loggingAspect, file);
        
        assertNotNull(result);
        assertTrue(result.toString().contains("MultipartFile"));
        assertTrue(result.toString().contains("test.xlsx"));
        assertTrue(result.toString().contains("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
    }
    
    @Test
    void testSafeSerializeLoggerObject() throws Exception {
        // 测试Logger对象的处理
        Logger logger = LoggerFactory.getLogger(LoggingAspectSerializationTest.class);
        
        // 使用反射调用私有方法sanitizeArgument
        Method sanitizeMethod = LoggingAspect.class.getDeclaredMethod("sanitizeArgument", Object.class);
        sanitizeMethod.setAccessible(true);
        
        Object result = sanitizeMethod.invoke(loggingAspect, logger);
        
        assertNotNull(result);
        assertTrue(result.toString().contains("Logger"));
    }
    
    @Test
    void testSafeSerializeArray() throws Exception {
        // 测试数组的处理
        Object[] array = {"test1", 123, true};
        
        String result = loggingObjectMapper.writeValueAsString(array);
        
        assertNotNull(result);
        assertTrue(result.contains("test1"));
        assertTrue(result.contains("123"));
        assertTrue(result.contains("true"));
    }
    
    @Test
    void testSafeSerializeNull() throws Exception {
        // 测试null值的处理
        String result = loggingObjectMapper.writeValueAsString(null);
        
        assertEquals("null", result);
    }
    
    @Test
    void testIsNonSerializable() throws Exception {
        // 测试不可序列化对象的检测
        Logger logger = LoggerFactory.getLogger(LoggingAspectSerializationTest.class);
        
        // 使用反射调用私有方法isNonSerializable
        Method isNonSerializableMethod = LoggingAspect.class.getDeclaredMethod("isNonSerializable", Object.class);
        isNonSerializableMethod.setAccessible(true);
        
        Boolean result = (Boolean) isNonSerializableMethod.invoke(loggingAspect, logger);
        
        assertTrue(result, "Logger对象应该被识别为不可序列化");
    }
    
    @Test
    void testSafeToString() throws Exception {
        // 测试安全toString方法
        TestObject testObj = new TestObject("test", 123);
        
        // 使用反射调用私有方法safeToString
        Method safeToStringMethod = LoggingAspect.class.getDeclaredMethod("safeToString", Object.class);
        safeToStringMethod.setAccessible(true);
        
        String result = (String) safeToStringMethod.invoke(loggingAspect, testObj);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
    
    @Test
    void testSafeToStringWithLongContent() throws Exception {
        // 测试长内容的截断
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 200; i++) {
            longContent.append("This is a very long string content. ");
        }
        
        TestObject testObj = new TestObject(longContent.toString(), 123);
        
        // 使用反射调用私有方法safeToString
        Method safeToStringMethod = LoggingAspect.class.getDeclaredMethod("safeToString", Object.class);
        safeToStringMethod.setAccessible(true);
        
        String result = (String) safeToStringMethod.invoke(loggingAspect, testObj);
        
        assertNotNull(result);
        // 验证内容被截断
        if (result.length() > 1000) {
            assertTrue(result.contains("(truncated)"));
        }
    }
    
    /**
     * 测试用的简单对象
     */
    static class TestObject {
        private String name;
        private int value;
        
        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public int getValue() { return value; }
        public void setValue(int value) { this.value = value; }
        
        @Override
        public String toString() {
            return "TestObject{name='" + name + "', value=" + value + "}";
        }
    }
}
